# Order Storage System Refactor

## Overview

This document outlines the comprehensive refactoring of the order storage system to ensure legal compliance, accurate tax handling, and robust financial record-keeping. The new system follows e-commerce best practices and prepares for future Stripe integration.

## Key Improvements

### 1. Enhanced Price Breakdown Storage

**Before:**

- Single `price` field per order item
- Basic `subtotal`, `total` fields
- Limited tax information

**After:**

- Detailed price breakdown with `unitPrice`, `unitPriceWithTax`, `totalPrice`
- Separate `taxAmount` and `taxRate` per item
- Comprehensive order-level price breakdown:
  - `subtotal` - Items total before tax and shipping
  - `taxAmount` - Total tax amount
  - `shippingAmount` - Shipping cost before tax
  - `shippingTaxAmount` - Tax on shipping
  - `discountAmount` - Total discount applied
  - `total` - Final total amount

### 2. Tax Compliance and Reporting

**New Tax Breakdown System:**

- `OrderTaxBreakdown` model stores detailed tax information by jurisdiction
- Supports multiple tax types: VAT, GST, sales tax, use tax
- Tracks tax rates, taxable amounts, and calculation methods
- Enables accurate tax reporting and compliance

**Tax Information Stored:**

- Jurisdiction (e.g., 'US-CA', 'DE', 'GB-VAT')
- Tax type and rate
- Taxable amount and tax amount
- Whether tax is inclusive or exclusive
- Calculation method used

### 3. Currency and Localization Support

**Enhanced Currency Handling:**

- `currency` field on both orders and order items
- `locale` field for proper localization
- `exchangeRate` field for currency conversion tracking
- `originalPrices` JSON field preserving multi-currency pricing

### 4. Comprehensive Shipping Information

**New OrderShipping Model:**

- Detailed shipping method, carrier, and service information
- Separate shipping cost and shipping tax tracking
- Delivery estimates and tracking number support
- Denormalized address information for performance

### 5. Enhanced Payment Transaction Tracking

**Improved Payment Records:**

- Detailed payment method and provider information
- Processing fees and net amount tracking
- Card details (last 4, brand, country) for reference
- Audit trail with IP address and user agent
- Support for multiple payment providers

### 6. Robust Refund Management

**Enhanced Refund Tracking:**

- Detailed refund amount breakdown (tax, shipping portions)
- Refund type classification (full, partial, shipping_only)
- Processing fees and net refund amounts
- Audit trail with approval workflow support

## Database Schema Changes

### Order Model Enhancements

```sql
-- New fields added to Order table
ALTER TABLE "Order" ADD COLUMN "tax_amount" INTEGER DEFAULT 0;
ALTER TABLE "Order" ADD COLUMN "shipping_amount" INTEGER DEFAULT 0;
ALTER TABLE "Order" ADD COLUMN "shipping_tax_amount" INTEGER DEFAULT 0;
ALTER TABLE "Order" ADD COLUMN "locale" TEXT DEFAULT 'en-US';
ALTER TABLE "Order" ADD COLUMN "exchange_rate" DECIMAL(10,6);
ALTER TABLE "Order" ADD COLUMN "tax_calculation_method" TEXT;
ALTER TABLE "Order" ADD COLUMN "tax_calculation_id" TEXT;
ALTER TABLE "Order" ADD COLUMN "tax_inclusive_pricing" BOOLEAN DEFAULT true;
ALTER TABLE "Order" ADD COLUMN "discount_type" TEXT;
ALTER TABLE "Order" ADD COLUMN "discount_value" INTEGER;
```

### OrderItem Model Enhancements

```sql
-- New fields added to OrderItem table
ALTER TABLE "OrderItem" ADD COLUMN "unit_price" INTEGER NOT NULL;
ALTER TABLE "OrderItem" ADD COLUMN "unit_price_with_tax" INTEGER NOT NULL;
ALTER TABLE "OrderItem" ADD COLUMN "total_price" INTEGER NOT NULL;
ALTER TABLE "OrderItem" ADD COLUMN "tax_amount" INTEGER DEFAULT 0;
ALTER TABLE "OrderItem" ADD COLUMN "tax_rate" DECIMAL(5,4);
ALTER TABLE "OrderItem" ADD COLUMN "sku" TEXT;
ALTER TABLE "OrderItem" ADD COLUMN "category" TEXT;
ALTER TABLE "OrderItem" ADD COLUMN "currency" TEXT DEFAULT 'USD';
ALTER TABLE "OrderItem" ADD COLUMN "original_prices" JSONB DEFAULT '{}';
ALTER TABLE "OrderItem" ADD COLUMN "discount_amount" INTEGER DEFAULT 0;
```

### New Models

- `OrderTaxBreakdown` - Detailed tax information by jurisdiction
- `OrderShipping` - Comprehensive shipping details and costs

## Implementation Guide

### 1. Creating Orders with New Schema

```typescript
const orderData: CreateOrderViewModel = {
	userId: user?.id,
	cartId: 'cart-123',
	items: [
		{
			productId: 'prod-123',
			variantId: 'var-456',
			quantity: 2,
			unitPrice: 1000, // $10.00 before tax
			unitPriceWithTax: 1200, // $12.00 including tax
			totalPrice: 2400, // $24.00 total for 2 items
			taxAmount: 400, // $4.00 total tax
			taxRate: 0.2, // 20% tax rate
			productName: 'Mechanical Keyboard',
			variantName: 'Cherry MX Blue',
			currency: 'USD',
			originalPrices: { USD: 1200, EUR: 1100, GBP: 950 }
		}
	],
	subtotal: 2000, // $20.00 items subtotal
	taxAmount: 400, // $4.00 total tax
	shippingAmount: 500, // $5.00 shipping
	shippingTaxAmount: 100, // $1.00 shipping tax
	discountAmount: 0,
	currency: 'USD',
	locale: 'en-US',
	taxInclusivePricing: true,
	taxCalculationMethod: 'stripe_tax'
};
```

### 2. Tax Breakdown Creation

```typescript
import { OrderTaxService } from '$lib/services/order-tax-service';

// Create tax breakdown from calculation result
await OrderTaxService.createTaxBreakdown({
	orderId: order.id,
	taxCalculation: taxResult,
	currency: 'USD'
});

// Create shipping details
await OrderTaxService.createShippingDetails(order.id, {
	method: 'standard',
	carrier: 'ups',
	baseAmount: 500,
	taxAmount: 100,
	totalAmount: 600,
	taxRate: 0.2,
	shippingAddress: {
		country: 'US',
		state: 'CA',
		city: 'San Francisco',
		postalCode: '94105'
	}
});
```

## Benefits

### 1. Legal Compliance

- Detailed tax records for audit purposes
- Proper jurisdiction-based tax tracking
- Support for international tax requirements (VAT, GST)

### 2. Financial Accuracy

- Precise price breakdowns prevent calculation errors
- Currency conversion tracking for international sales
- Comprehensive refund and payment tracking

### 3. Reporting and Analytics

- Detailed tax reporting by jurisdiction
- Revenue analysis by currency and locale
- Shipping cost analysis and optimization

### 4. Future-Proof Architecture

- Ready for Stripe Tax integration
- Supports multiple payment providers
- Scalable for international expansion

### 5. Customer Transparency

- Clear price breakdowns for customers
- Detailed tax information display
- Comprehensive order confirmations

## Migration Strategy

### Phase 1: Database Schema Migration

```bash
# Generate and apply Prisma migration
npx prisma migrate dev --name order-storage-refactor
npx prisma generate
```

### Phase 2: Update Existing Code

1. **Update Order Repository**: Already completed in this refactor
2. **Update Checkout Process**: Modify checkout to use new order structure
3. **Update Order Display**: Update order confirmation and history pages
4. **Update Admin Interface**: Modify admin order management

### Phase 3: Data Migration (if needed)

```typescript
// Example migration script for existing orders
async function migrateExistingOrders() {
	const orders = await prisma.order.findMany({
		where: { taxAmount: null }, // Find orders without new fields
		include: { items: true }
	});

	for (const order of orders) {
		// Calculate missing fields from existing data
		const taxAmount = calculateTaxFromItems(order.items);
		const shippingAmount = order.total - order.subtotal - taxAmount;

		await prisma.order.update({
			where: { id: order.id },
			data: {
				taxAmount,
				shippingAmount,
				shippingTaxAmount: 0,
				locale: 'en-US',
				taxInclusivePricing: true
			}
		});
	}
}
```

### Phase 4: Testing and Validation

1. **Unit Tests**: Test order creation and retrieval
2. **Integration Tests**: Test complete checkout flow
3. **Data Validation**: Verify calculations and data integrity
4. **Performance Testing**: Ensure queries perform well

## Completed Work Summary

### ✅ Database Schema Updates

- Enhanced Order model with detailed price breakdown fields
- Enhanced OrderItem model with comprehensive pricing information
- Added OrderTaxBreakdown model for detailed tax compliance
- Added OrderShipping model for comprehensive shipping tracking
- Enhanced PaymentTransaction model with detailed payment information
- Enhanced Refund model with comprehensive refund tracking

### ✅ Code Updates

- Updated OrderRepository with new schema support
- Updated order models and view models
- Created OrderTaxService for tax breakdown management
- Updated validation and mapping functions
- Generated new Prisma client with updated schema

### ✅ Seed File Updates

- Updated prisma/seed.js to work with current database schema
- Synchronized variant prices and data with existing database values
- Removed order-related cleanup to preserve existing order data
- Fixed schema compatibility issues

### ✅ Documentation

- Created comprehensive refactor documentation
- Documented migration strategy and best practices
- Provided implementation examples and usage patterns

## Current Status

The order storage system has been successfully refactored with:

- **Legal Compliance**: Detailed tax breakdown storage for audit purposes
- **Financial Accuracy**: Comprehensive price tracking and currency support
- **Future-Ready**: Prepared for Stripe integration and international expansion
- **Data Integrity**: Preserved existing order data while upgrading schema

## Next Steps

1. **Testing**: Run comprehensive tests on the updated order system
2. **Stripe Integration**: Implement Stripe Tax API integration
3. **International Support**: Add more currencies and tax jurisdictions
4. **Reporting Dashboard**: Build admin interface for tax and financial reporting
5. **Audit Tools**: Create tools for financial auditing and compliance
6. **Performance Optimization**: Optimize queries for large order volumes

This refactored system provides a solid foundation for handling complex e-commerce transactions while maintaining compliance with international tax regulations and providing detailed financial tracking.
