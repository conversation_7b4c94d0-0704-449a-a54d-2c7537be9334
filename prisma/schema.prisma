generator client {
  provider      = "prisma-client-js"
  output        = "../node_modules/.prisma/client"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
  engineType    = "binary"
}

generator seed {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                 String                     @id @default(uuid())
  provider           String
  providerId         String                     @map("provider_id")
  email              String                     @unique
  image              String?
  firstName          String                     @map("first_name")
  lastName           String                     @map("last_name")
  passwordHash       String?                    @map("password_hash")
  status             String                     @default("active")
  emailVerified      <PERSON>olean                    @default(false) @map("email_verified")
  isAd<PERSON>                    @default(false) @map("is_admin")
  stripeCustomerId   String?                    @map("stripe_customer_id")
  lastLoginAt        DateTime?                  @map("last_login_at")
  createdAt          DateTime                   @default(now()) @map("created_at")
  updatedAt          DateTime                   @updatedAt @map("updated_at")
  carts              Cart[]
  emailVerifications EmailVerificationRequest[]
  orders             Order[]
  passwordResets     PasswordResetSession[]
  sessions           Session[]

  @@unique([provider, providerId])
  @@index([email])
  @@index([stripeCustomerId])
  @@index([status])
}

model Session {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  expiresAt DateTime @map("expires_at")
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
}

model EmailVerificationRequest {
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  email     String
  code      String
  expiresAt DateTime @map("expires_at")
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
}

model PasswordResetSession {
  id            String   @id @default(uuid())
  userId        String   @map("user_id")
  email         String
  code          String
  expiresAt     DateTime @map("expires_at")
  emailVerified Boolean  @default(false) @map("email_verified")
  user          User     @relation(fields: [userId], references: [id])

  @@index([userId])
}

model Product {
  id             String           @id @default(uuid())
  slug           String           @unique
  category       String
  name           String
  descriptions   Json             @default("{}")
  features       Json             @default("{}")
  specifications Json             @default("{}")
  isAccessory    Boolean          @default(false) @map("is_accessory")
  createdAt      DateTime         @default(now()) @map("created_at")
  updatedAt      DateTime         @updatedAt @map("updated_at")
  cartItems      CartItem[]
  orderItems     OrderItem[]
  images         ProductImage[]
  variants       ProductVariant[]

  @@index([slug])
  @@index([category])
}

model ProductVariant {
  id                    String                 @id @default(uuid())
  productId             String                 @map("product_id")
  sku                   String                 @unique
  name                  String
  prices                Json                   @default("{}")
  stockQuantity         Int                    @default(0) @map("stock_quantity")
  attributes            Json                   @default("{}")
  createdAt             DateTime               @default(now()) @map("created_at")
  updatedAt             DateTime               @updatedAt @map("updated_at")
  cartItems             CartItem[]
  inventoryTransactions InventoryTransaction[]
  orderItems            OrderItem[]
  product               Product                @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@index([sku])
}

model ProductImage {
  id        String  @id @default(uuid())
  productId String  @map("product_id")
  url       String
  alt       String
  position  Int
  product   Product @relation(fields: [productId], references: [id])

  @@index([productId])
  @@index([position])
}

model Cart {
  id             String     @id @default(uuid())
  userId         String?    @map("user_id")
  sessionId      String     @map("session_id")
  status         String     @default("active")
  email          String?
  firstName      String?    @map("first_name")
  lastName       String?    @map("last_name")
  lastActivityAt DateTime   @default(now()) @map("last_activity_at")
  createdAt      DateTime   @default(now()) @map("created_at")
  updatedAt      DateTime   @updatedAt @map("updated_at")
  user           User?      @relation(fields: [userId], references: [id])
  items          CartItem[]

  @@index([userId])
  @@index([sessionId])
  @@index([email])
  @@index([status])
  @@index([lastActivityAt])
}

model CartItem {
  id         String         @id @default(uuid())
  cartId     String         @map("cart_id")
  productId  String         @map("product_id")
  variantId  String         @map("variant_id")
  quantity   Int            @default(1)
  prices     Json           @default("{}")
  composites Json           @default("[]")
  createdAt  DateTime       @default(now()) @map("created_at")
  updatedAt  DateTime       @updatedAt @map("updated_at")
  cart       Cart           @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product    Product        @relation(fields: [productId], references: [id])
  variant    ProductVariant @relation(fields: [variantId], references: [id])

  @@index([cartId])
  @@index([productId])
  @@index([variantId])
  @@index([cartId, variantId])
}



model Order {
  id                    String                 @id @default(uuid())
  userId                String?                @map("user_id")
  cartId                String                 @map("cart_id")
  status                String                 @default("pending_payment")
  email                 String
  firstName             String                 @map("first_name")
  lastName              String                 @map("last_name")
  subtotal              Int
  taxAmount             Int                    @default(0) @map("tax_amount")
  taxRate               Decimal?               @map("tax_rate") @db.Decimal(6, 4)
  shippingAmount        Int                    @default(0) @map("shipping_amount")
  total                 Int
  currency              String                 @default("USD")
  locale                String                 @default("en-US")
  stripePaymentIntentId String?                @map("stripe_payment_intent_id")
  stripeClientSecret    String?                @map("stripe_client_secret")
  createdAt             DateTime               @default(now()) @map("created_at")
  updatedAt             DateTime               @updatedAt @map("updated_at")
  inventoryTransactions InventoryTransaction[]
  user                  User?                  @relation(fields: [userId], references: [id])
  addresses             OrderAddress[]
  items                 OrderItem[]
  statusHistory         OrderStatusHistory[]
  paymentTransactions   PaymentTransaction[]
  refunds               Refund[]

  @@index([userId])
  @@index([cartId])
  @@index([email])
  @@index([status])
  @@index([stripePaymentIntentId])
  @@index([currency])
  @@index([locale])
}

model OrderItem {
  id               String         @id @default(uuid())
  orderId          String         @map("order_id")
  productId        String         @map("product_id")
  variantId        String         @map("variant_id")
  quantity         Int
  unitPrice        Int            @map("unit_price")
  unitPriceWithTax Int            @map("unit_price_with_tax")
  totalPrice       Int            @map("total_price")
  taxAmount        Int            @default(0) @map("tax_amount")
  taxRate          Decimal?       @map("tax_rate") @db.Decimal(5, 4)
  name             String
  variantName      String         @map("variant_name")
  sku              String?
  category         String?
  currency         String         @default("USD")
  originalPrices   Json           @default("{}")
  composites       Json           @default("[]")
  createdAt        DateTime       @default(now()) @map("created_at")
  updatedAt        DateTime       @updatedAt @map("updated_at")
  order            Order          @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product          Product        @relation(fields: [productId], references: [id])
  variant          ProductVariant @relation(fields: [variantId], references: [id])

  @@index([orderId])
  @@index([productId])
  @@index([variantId])
  @@index([sku])
}

model OrderAddress {
  id         String   @id @default(uuid())
  orderId    String   @map("order_id")
  type       String
  firstName  String   @map("first_name")
  lastName   String   @map("last_name")
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String   @map("postal_code")
  country    String
  phone      String?
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  order      Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([type])
}

model OrderStatusHistory {
  id        String   @id @default(uuid())
  orderId   String   @map("order_id")
  status    String
  note      String?
  createdAt DateTime @default(now()) @map("created_at")
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([status])
}



model PaymentTransaction {
  id                    String    @id @default(uuid())
  orderId               String    @map("order_id")
  status                String    @default("pending")
  amount                Int
  currency              String    @default("USD")
  exchangeRate          Decimal?  @map("exchange_rate") @db.Decimal(10, 6)
  paymentMethod         String    @map("payment_method")
  paymentProvider       String    @default("stripe") @map("payment_provider")
  stripePaymentIntentId String?   @map("stripe_payment_intent_id")
  stripePaymentMethodId String?   @map("stripe_payment_method_id")
  stripeChargeId        String?   @map("stripe_charge_id")
  cardLast4             String?   @map("card_last4")
  cardBrand             String?   @map("card_brand")
  cardCountry           String?   @map("card_country")
  processingFee         Int?      @map("processing_fee")
  netAmount             Int?      @map("net_amount")
  errorMessage          String?   @map("error_message")
  errorCode             String?   @map("error_code")
  ipAddress             String?   @map("ip_address")
  userAgent             String?   @map("user_agent")
  processedAt           DateTime? @map("processed_at")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")
  order                 Order     @relation(fields: [orderId], references: [id])
  refunds               Refund[]

  @@index([orderId])
  @@index([stripePaymentIntentId])
  @@index([status])
  @@index([paymentProvider])
  @@index([processedAt])
}

model Refund {
  id             String             @id @default(uuid())
  orderId        String             @map("order_id")
  transactionId  String             @map("transaction_id")
  amount         Int
  taxAmount      Int                @default(0) @map("tax_amount")
  shippingAmount Int                @default(0) @map("shipping_amount")
  currency       String             @default("USD")
  type           String             @default("full")
  reason         String
  note           String?
  status         String             @default("pending")
  stripeRefundId String?            @map("stripe_refund_id")
  stripeChargeId String?            @map("stripe_charge_id")
  processingFee  Int?               @map("processing_fee")
  netRefund      Int?               @map("net_refund")
  errorMessage   String?            @map("error_message")
  errorCode      String?            @map("error_code")
  initiatedBy    String?            @map("initiated_by")
  approvedBy     String?            @map("approved_by")
  processedAt    DateTime?          @map("processed_at")
  createdAt      DateTime           @default(now()) @map("created_at")
  updatedAt      DateTime           @updatedAt @map("updated_at")
  order          Order              @relation(fields: [orderId], references: [id])
  transaction    PaymentTransaction @relation(fields: [transactionId], references: [id])

  @@index([orderId])
  @@index([transactionId])
  @@index([status])
  @@index([type])
  @@index([processedAt])
}

model InventoryTransaction {
  id        String         @id @default(uuid())
  variantId String         @map("variant_id")
  quantity  Int
  type      String
  note      String?
  createdAt DateTime       @default(now()) @map("created_at")
  orderId   String?        @map("order_id")
  order     Order?         @relation(fields: [orderId], references: [id])
  variant   ProductVariant @relation(fields: [variantId], references: [id])

  @@index([variantId])
  @@index([type])
  @@index([orderId])
}
