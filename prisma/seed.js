import { PrismaClient } from '@prisma/client';

// Initialize Prisma client
const prisma = new PrismaClient();

async function main() {
	try {
		// Clear existing product data only (preserve orders and user data)
		console.log('Cleaning up existing product data...');

		// First, delete cart items and order items that reference products
		await prisma.orderItem.deleteMany();
		await prisma.cartItem.deleteMany();

		// Then delete inventory transactions that reference variants
		await prisma.inventoryTransaction.deleteMany();

		// Now it's safe to delete product-related data
		await prisma.productImage.deleteMany();
		await prisma.productVariant.deleteMany();
		await prisma.product.deleteMany();

		console.log('Product data cleaned. Creating new products...');

		// Insert products
		await prisma.product.createMany({
			data: [
				{
					id: '550e8400-e29b-41d4-a716-446655440000',
					slug: 'zero',
					category: 'KEYBOARD',
					name: '<PERSON>',
					descriptions: {
						'en-US':
							'A premium low-profile mechanical keyboard with a sleek design and exceptional typing experience.',
						'en-UK':
							'A premium low-profile mechanical keyboard with a sleek design and exceptional typing experience.',
						'de-DE':
							'Eine hochwertige Low-Profile-Mechaniktastatur mit elegantem Design und außergewöhnlichem Tippgefühl.',
						'fr-FR':
							'Un clavier mécanique premium à profil bas avec un design élégant et une expérience de frappe exceptionnelle.',
						'zh-CN': '一款优质的低配机械键盘，设计时尚，打字体验卓越。'
					},
					features: {
						'en-US': ['Low-profile mechanical switches', 'Aluminum frame', 'RGB backlighting'],
						'en-UK': ['Low-profile mechanical switches', 'Aluminium frame', 'RGB backlighting'],
						'de-DE': ['Low-Profile-Mechanikschalter', 'Aluminiumrahmen', 'RGB-Beleuchtung'],
						'fr-FR': [
							'Interrupteurs mécaniques à profil bas',
							'Cadre en aluminium',
							'Rétroéclairage RGB'
						],
						'zh-CN': ['低配机械轴', '铝制框架', 'RGB背光']
					},
					specifications: {
						'en-US': {
							dimensions: '320 x 120 x 25mm',
							weight: '600g',
							connectivity: 'USB-C',
							switches: 'Low-profile mechanical'
						},
						'en-UK': {
							dimensions: '320 x 120 x 25mm',
							weight: '600g',
							connectivity: 'USB-C',
							switches: 'Low-profile mechanical'
						},
						'de-DE': {
							dimensions: '320 x 120 x 25mm',
							weight: '600g',
							connectivity: 'USB-C',
							switches: 'Low-Profile-Mechanik'
						},
						'fr-FR': {
							dimensions: '320 x 120 x 25mm',
							weight: '600g',
							connectivity: 'USB-C',
							switches: 'Mécanique à profil bas'
						},
						'zh-CN': {
							dimensions: '320 x 120 x 25mm',
							weight: '600g',
							connectivity: 'USB-C',
							switches: '低配机械轴'
						}
					},
					isAccessory: false
				},
				{
					id: '550e8400-e29b-41d4-a716-446655440001',
					slug: 'kailh-choc-v2-switches',
					category: 'SWITCH',
					name: 'Kailh Choc V2 Switches',
					descriptions: {
						'en-US': 'Low-profile mechanical switches perfect for slim keyboards.',
						'en-UK': 'Low-profile mechanical switches perfect for slim keyboards.',
						'de-DE': 'Low-Profile-Mechanikschalter, perfekt für schlanke Tastaturen.',
						'fr-FR': 'Interrupteurs mécaniques à profil bas parfaits pour les claviers minces.',
						'zh-CN': '适合超薄键盘的低配机械轴。'
					},
					features: {
						'en-US': [
							'Low-profile design',
							'Smooth linear action',
							'Durable construction',
							'Compatible with most low-profile keyboards'
						],
						'en-UK': [
							'Low-profile design',
							'Smooth linear action',
							'Durable construction',
							'Compatible with most low-profile keyboards'
						],
						'de-DE': [
							'Low-Profile-Design',
							'Sanfte lineare Bewegung',
							'Langlebige Konstruktion',
							'Kompatibel mit den meisten Low-Profile-Tastaturen'
						],
						'fr-FR': [
							'Design à profil bas',
							'Action linéaire fluide',
							'Construction durable',
							'Compatible avec la plupart des claviers à profil bas'
						],
						'zh-CN': ['低配设计', '顺滑线性触感', '耐用结构', '兼容大多数低配键盘']
					},
					specifications: {
						'en-US': {
							type: 'Linear',
							actuationForce: '50g',
							travelDistance: '1.5mm',
							lifespan: '50 million keystrokes'
						},
						'en-UK': {
							type: 'Linear',
							actuationForce: '50g',
							travelDistance: '1.5mm',
							lifespan: '50 million keystrokes'
						},
						'de-DE': {
							type: 'Linear',
							actuationForce: '50g',
							travelDistance: '1,5mm',
							lifespan: '50 Millionen Tastendrücke'
						},
						'fr-FR': {
							type: 'Linéaire',
							actuationForce: '50g',
							travelDistance: '1,5mm',
							lifespan: '50 millions de frappes'
						},
						'zh-CN': {
							type: '线性',
							actuationForce: '50g',
							travelDistance: '1.5毫米',
							lifespan: '5000万次按键寿命'
						}
					},
					isAccessory: true
				},
				{
					id: '550e8400-e29b-41d4-a716-446655440002',
					slug: 'tht-low-profile-keycaps',
					category: 'KEYCAP',
					name: 'THT Low Profile Keycaps',
					descriptions: {
						'en-US': 'Premium low-profile keycaps designed for mechanical keyboards.',
						'en-UK': 'Premium low-profile keycaps designed for mechanical keyboards.',
						'de-DE': 'Hochwertige Low-Profile-Keycaps für mechanische Tastaturen.',
						'fr-FR':
							'Capuchons de touches à profil bas premium conçus pour les claviers mécaniques.',
						'zh-CN': '为机械键盘设计的优质低配键帽。'
					},
					features: {
						'en-US': [
							'Low-profile design',
							'PBT material',
							'Durable construction',
							'Compatible with most low-profile switches'
						],
						'en-UK': [
							'Low-profile design',
							'PBT material',
							'Durable construction',
							'Compatible with most low-profile switches'
						],
						'de-DE': [
							'Low-Profile-Design',
							'PBT-Material',
							'Langlebige Konstruktion',
							'Kompatibel mit den meisten Low-Profile-Schaltern'
						],
						'fr-FR': [
							'Design à profil bas',
							'Matériau PBT',
							'Construction durable',
							'Compatible avec la plupart des interrupteurs à profil bas'
						],
						'zh-CN': ['低配设计', 'PBT材质', '耐用结构', '兼容大多数低配轴']
					},
					specifications: {
						'en-US': {
							material: 'PBT',
							profile: 'Low',
							compatibility: 'Kailh Choc, Gateron Low Profile',
							thickness: '1.2mm'
						},
						'en-UK': {
							material: 'PBT',
							profile: 'Low',
							compatibility: 'Kailh Choc, Gateron Low Profile',
							thickness: '1.2mm'
						},
						'de-DE': {
							material: 'PBT',
							profile: 'Low',
							compatibility: 'Kailh Choc, Gateron Low Profile',
							thickness: '1,2mm'
						},
						'fr-FR': {
							material: 'PBT',
							profile: 'Bas',
							compatibility: 'Kailh Choc, Gateron Low Profile',
							thickness: '1,2mm'
						},
						'zh-CN': {
							material: 'PBT',
							profile: '低',
							compatibility: 'Kailh Choc, Gateron 低配',
							thickness: '1.2毫米'
						}
					},
					isAccessory: true
				}
			]
		});

		// Insert variants
		await prisma.productVariant.createMany({
			data: [
				{
					id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a18',
					productId: '550e8400-e29b-41d4-a716-446655440001',
					sku: 'SW-CHOC-BLUE',
					name: 'Kailh Choc V2 Blue Switch',
					prices: {
						CNY: 19900,
						EUR: 2500,
						GBP: 2100,
						USD: 2900
					},
					stockQuantity: 50,
					attributes: {
						feel: 'Clicky',
						type: 'Tactile',
						color: 'Blue',
						stemType: 'Kailh Choc V2',
						actuation_force: '50g'
					}
				},
				{
					id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a19',
					productId: '550e8400-e29b-41d4-a716-446655440001',
					sku: 'SW-CHOC-BROWN',
					name: 'Kailh Choc V2 Brown Switch',
					prices: {
						CNY: 19900,
						EUR: 2500,
						GBP: 2100,
						USD: 2900
					},
					stockQuantity: 50,
					attributes: {
						feel: 'Tactile',
						type: 'Tactile',
						color: 'Brown',
						stemType: 'Kailh Choc V2',
						actuation_force: '45g'
					}
				},
				{
					id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
					productId: '550e8400-e29b-41d4-a716-446655440000',
					sku: 'KB-THYP-ERG-CHOC',
					name: 'Zero - Ergonomic Layout',
					prices: {
						CNY: 179000,
						EUR: 21900,
						GBP: 18500,
						USD: 24900
					},
					stockQuantity: 50,
					attributes: {
						layout: 'split',
						compatibleWith: { SWITCH: { stemType: ['Kailh Choc V1', 'Kailh Choc V2'] } },
						keyboard_variant: 'ergonomic',
						requiredAccessories: ['SWITCH', 'KEYCAP']
					}
				},
				{
					id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a20',
					productId: '550e8400-e29b-41d4-a716-446655440002',
					sku: 'KC-THT-CHAR',
					name: 'THT Low Profile Keycap Set - Characters',
					prices: {
						CNY: 28900,
						EUR: 3500,
						GBP: 2900,
						USD: 3900
					},
					stockQuantity: 50,
					attributes: {
						color: 'Black',
						layout: 'Split',
						material: 'PBT',
						stemType: ['MX', 'Kailh Choc V2'],
						legend_type: 'Characters'
					}
				},
				{
					id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a21',
					productId: '550e8400-e29b-41d4-a716-446655440002',
					sku: 'KC-THT-DOTS',
					name: 'THT Low Profile Keycap Set - Dots',
					prices: {
						CNY: 28900,
						EUR: 3500,
						GBP: 2900,
						USD: 3900
					},
					stockQuantity: 50,
					attributes: {
						color: 'Black',
						layout: 'Split',
						material: 'PBT',
						stemType: ['MX', 'Kailh Choc V2'],
						legend_type: 'Dots'
					}
				},
				{
					id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a17',
					productId: '550e8400-e29b-41d4-a716-446655440001',
					sku: 'SW-CHOC-RED',
					name: 'Kailh Choc V2 Red Switch',
					prices: {
						CNY: 19900,
						EUR: 2500,
						GBP: 2100,
						USD: 2900
					},
					stockQuantity: 50,
					attributes: {
						feel: 'Smooth',
						type: 'Linear',
						color: 'Red',
						stemType: 'Kailh Choc V2',
						actuation_force: '43g'
					}
				}
			]
		});

		// Insert images
		await prisma.productImage.createMany({
			data: [
				// Zero Keyboard Images
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
					productId: '550e8400-e29b-41d4-a716-446655440000',
					url: '/images/products/zero/main.png',
					alt: 'Zero Split Keyboard - Main View',
					position: 1
				},
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
					productId: '550e8400-e29b-41d4-a716-446655440000',
					url: '/images/products/zero/angle.png',
					alt: 'Zero Split Keyboard - Angled View',
					position: 2
				},
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d481',
					productId: '550e8400-e29b-41d4-a716-446655440000',
					url: '/images/products/zero/angle_compact.png',
					alt: 'Zero Split Keyboard - Compact Angled View',
					position: 3
				},
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d482',
					productId: '550e8400-e29b-41d4-a716-446655440000',
					url: '/images/products/zero/cross_bottom.png',
					alt: 'Zero Split Keyboard - Cross Section Bottom',
					position: 4
				},
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d483',
					productId: '550e8400-e29b-41d4-a716-446655440000',
					url: '/images/products/zero/cross_top.png',
					alt: 'Zero Split Keyboard - Cross Section Top',
					position: 5
				},
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d484',
					productId: '550e8400-e29b-41d4-a716-446655440000',
					url: '/images/products/zero/cross_usb.png',
					alt: 'Zero Split Keyboard - USB-C Port',
					position: 6
				},

				// Kailh Choc V2 Switches Images
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d485',
					productId: '550e8400-e29b-41d4-a716-446655440001',
					url: '/images/products/switches/all.png',
					alt: 'Kailh Choc V2 Switches - All Colors',
					position: 1
				},
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d486',
					productId: '550e8400-e29b-41d4-a716-446655440001',
					url: '/images/products/switches/blue.png',
					alt: 'Kailh Choc V2 Switches - Blue',
					position: 2
				},
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d487',
					productId: '550e8400-e29b-41d4-a716-446655440001',
					url: '/images/products/switches/brown.png',
					alt: 'Kailh Choc V2 Switches - Brown',
					position: 3
				},
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d488',
					productId: '550e8400-e29b-41d4-a716-446655440001',
					url: '/images/products/switches/red.png',
					alt: 'Kailh Choc V2 Switches - Red',
					position: 4
				},

				// THT Low Profile Keycaps Images
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d489',
					productId: '550e8400-e29b-41d4-a716-446655440002',
					url: '/images/products/keycaps/chars.png',
					alt: 'THT Low Profile Keycaps - Characters',
					position: 1
				},
				{
					id: 'f47ac10b-58cc-4372-a567-0e02b2c3d490',
					productId: '550e8400-e29b-41d4-a716-446655440002',
					url: '/images/products/keycaps/dots.png',
					alt: 'THT Low Profile Keycaps - Dots',
					position: 2
				}
			]
		});
	} catch (error) {
		console.error('Error seeding database:', error);
		throw error;
	} finally {
		await prisma.$disconnect();
	}
}

main().catch((e) => {
	console.error(e);
	process.exit(1);
});
