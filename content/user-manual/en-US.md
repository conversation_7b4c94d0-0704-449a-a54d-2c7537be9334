# User Manual: Zero Split Ergonomic Keyboard by Onspry

Welcome to your new **Zero Split Ergonomic Keyboard** by **Onspry**. This guide will help you set up your keyboard quickly and troubleshoot any issues you may encounter.

---

## What's in the Box

- 1× Left keyboard half (with USB-C to PC connection)
- 1× Right keyboard half
- 1× USB-C to USB-C cable (1 meter) for connecting to your PC
- 1× USB-C to USB-C interconnect cable (25 cm) for linking both halves

---

## Getting Started

### Step 1: Connect the Two Halves

Use the **included 25 cm USB-C cable** to connect the **left** and **right** halves of the keyboard.

- Plug one end of the cable into the **USB-C port on the left half**.
- Plug the other end into the **USB-C port on the right half**.

![Zero Connected Keyboard](/user-manual/zero-connected.svg)

> 💡 _Tip: The interconnect cable is reversible—there is no dedicated "in" or "out" port on either side._

### Step 2: Connect to Your Computer

Use the **1 meter USB-C to USB-C cable** to connect the **left half** to your **computer's USB-C port**.

- Plug one end into the **USB-C port on the left half**.
- Plug the other end into your **computer**.

![Zero Connected Keyboard](/user-manual/zero-connected-laptop.svg)

> 🔌 _Only the left half needs to be connected to the computer. The right half draws both power and data through the interconnect cable._

---

## You're Ready to Type!

Your operating system should automatically recognize the keyboard. No additional drivers are needed for basic functionality.

---

## Troubleshooting

| Issue                       | Solution                                                |
| --------------------------- | ------------------------------------------------------- |
| Only one half is responsive | Ensure the interconnect cable is securely connected.    |
| Keyboard not detected       | Try a different USB-C port or cable.                    |
| Slow or unresponsive keys   | Check all cable connections and try restarting your PC. |

---

## Firmware Updates & Keymap Customization

The Zero keyboard supports advanced customization via **QMK** or **VIA**.

- Customize key mappings, layers, and macros.
- Download firmware or configuration tools from: [onspry.com/firmware](https://onspry.com/firmware)

> ⚠️ _Modifying firmware is optional and intended for advanced users._

---

## 🎹 Key Customization with Vial

The Zero keyboard supports key customization through the **Vial** application, a user-friendly tool for configuring your keyboard layout.

### Using Vial Web

1. Visit [vial.rocks](https://vial.rocks) in your web browser
2. Connect your keyboard to your computer
3. The keyboard must be in unlocked mode to proceed
4. Press and hold the specified keys until the progress bar fills up
5. Once connected, you can:
   - Remap keys
   - Create custom layers
   - Configure macros
   - Adjust keyboard settings

> 💡 _Tip: For the best experience, use the latest versions of Chrome, Chromium, or Edge browsers._

### Using Vial Desktop App

For offline use or additional features:

1. Download the standalone Vial application from [get.vial.today](https://get.vial.today)
2. Install and launch the application
3. Connect your keyboard
4. Follow the same unlocking process as the web version
5. Customize your keyboard layout as needed

> ⚠️ _Security Note: Always perform keyboard customization on trusted computers. Remember to lock your keyboard after making changes by selecting Security->Lock from the menu or by replugging the keyboard._

---

## 🧰 Support

Need help or have a question?

- Visit: **[onspry.com/support](https://onspry.com/support)**
- Email: **[<EMAIL>](mailto:<EMAIL>)**

We're here to help you every step of the way.

---

## 📝 Final Tips

- Keep cables neatly arranged to prevent wear or accidental disconnection.
- Avoid plugging or unplugging the interconnect cable while typing.
- For enhanced comfort, consider using a wrist rest or split keyboard tenting accessories.

---

Thanks for choosing the **Zero Keyboard** by **Onspry** — where comfort meets precision.

**Happy typing!**
