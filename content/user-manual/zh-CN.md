# 📘 用户手册：Onspry Zero 分体人体工学键盘

欢迎使用您的新 **Onspry Zero 分体人体工学键盘**。本指南将帮助您快速设置键盘并排查可能遇到的问题。

---

## 📦 包装清单

- 1× 左键盘半边（带 USB-C 到 PC 连接）
- 1× 右键盘半边
- 1× USB-C 转 USB-C 数据线（1 米），用于连接电脑
- 1× USB-C 转 USB-C 互连线（25 厘米），用于连接两半键盘

---

## 🔌 快速开始

### 步骤 1：连接两半键盘

使用**随附的 25 厘米 USB-C 线**连接键盘的**左半边**和**右半边**。

- 将一端插入**左半边的 USB-C 接口**。
- 将另一端插入**右半边的 USB-C 接口**。

![Zero Connected Keyboard](/user-manual/zero-connected.svg)

> 💡 _提示：互连线可正反插——两侧没有专用的"输入"或"输出"端口。_

### 步骤 2：连接电脑

使用**1 米 USB-C 转 USB-C 线**将**左半边**连接到**电脑的 USB-C 接口**。

- 一端插入**左半边的 USB-C 接口**。
- 另一端插入您的**电脑**。

![Zero Connected Keyboard](/user-manual/zero-connected-laptop.svg)

> 🔌 _只需将左半边连接到电脑。右半边通过互连线获取电源和数据。_

---

## ✅ 开始打字！

您的操作系统应能自动识别该键盘。基本功能无需额外驱动。

---

## 🛠️ 故障排除

| 问题               | 解决方法                      |
| ------------------ | ----------------------------- |
| 只有一半有反应     | 确保互连线已牢固连接。        |
| 键盘无法识别       | 尝试更换 USB-C 接口或数据线。 |
| 按键反应慢或无反应 | 检查所有线缆连接并重启电脑。  |

---

## 🔧 固件更新与键位自定义

Zero 键盘支持通过 **QMK** 或 **VIA** 进行高级自定义。

- 自定义按键映射、层和宏。
- 固件及配置工具下载地址：[onspry.com/firmware](https://onspry.com/firmware)

> ⚠️ _固件修改为可选项，仅建议高级用户操作。_

---

## 🎹 使用 Vial 自定义按键

Zero 键盘支持通过 **Vial** 应用程序进行按键自定义，这是一个用户友好的键盘配置工具。

### 使用 Vial Web

1. 在浏览器中访问 [vial.rocks](https://vial.rocks)
2. 将键盘连接到电脑
3. 键盘必须处于解锁模式才能继续
4. 按住指定按键直到进度条填满
5. 连接成功后，您可以：
   - 重新映射按键
   - 创建自定义层
   - 配置宏
   - 调整键盘设置

> 💡 _提示：为获得最佳体验，请使用最新版本的 Chrome、Chromium 或 Edge 浏览器。_

### 使用 Vial 桌面应用

用于离线使用或获取额外功能：

1. 从 [get.vial.today](https://get.vial.today) 下载独立的 Vial 应用程序
2. 安装并启动应用程序
3. 连接键盘
4. 按照与网页版相同的解锁流程操作
5. 根据需要自定义键盘布局

> ⚠️ _安全提示：请在可信的电脑上进行键盘自定义。完成更改后，记得通过选择菜单中的安全->锁定或重新插拔键盘来锁定键盘。_

---

## 🧰 支持

需要帮助或有疑问？

- 访问：**[onspry.com/support](https://onspry.com/support)**
- 邮箱：**[<EMAIL>](mailto:<EMAIL>)**

我们随时为您提供帮助。

---

## 📝 温馨提示

- 整理好线缆，防止磨损或意外断开。
- 打字时避免插拔互连线。
- 建议搭配腕托或分体键盘支架以提升舒适度。

---

感谢您选择 **Onspry Zero 键盘** —— 舒适与精准的结合。

**祝您打字愉快！**
