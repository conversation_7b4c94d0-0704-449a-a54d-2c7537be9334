# 📘 Manuel d'utilisation : Clavier ergonomique Zero Split par Onspry

Bienvenue dans votre nouveau **clavier ergonomique Zero Split** de **Onspry**. Ce guide vous aidera à configurer rapidement votre clavier et à résoudre tout problème éventuel.

---

## 📦 Contenu de la boîte

- 1× Moitié gauche du clavier (avec connexion USB-C vers PC)
- 1× Moitié droite du clavier
- 1× Câble USB-C vers USB-C (1 mètre) pour connecter à votre PC
- 1× Câble d'interconnexion USB-C vers USB-C (25 cm) pour relier les deux moitiés

---

## 🔌 Mise en route

### Étape 1 : Connecter les deux moitiés

Utilisez le **câble USB-C de 25 cm fourni** pour connecter les moitiés **gauche** et **droite** du clavier.

- Branchez une extrémité du câble sur le **port USB-C de la moitié gauche**.
- Branchez l'autre extrémité sur le **port USB-C de la moitié droite**.

![Zero Connected Keyboard](/user-manual/zero-connected.svg)

> 💡 _Astuce : le câble d'interconnexion est réversible — il n'y a pas de port « entrée » ou « sortie » dédié de chaque côté._

### Étape 2 : Connecter à votre ordinateur

Utilisez le **câble USB-C vers USB-C de 1 mètre** pour connecter la **moitié gauche** au **port USB-C de votre ordinateur**.

- Branchez une extrémité sur le **port USB-C de la moitié gauche**.
- Branchez l'autre extrémité sur votre **ordinateur**.

![Zero Connected Keyboard](/user-manual/zero-connected-laptop.svg)

> 🔌 _Seule la moitié gauche doit être connectée à l'ordinateur. La moitié droite reçoit l'alimentation et les données via le câble d'interconnexion._

---

## ✅ Prêt à taper !

Votre système d'exploitation devrait reconnaître automatiquement le clavier. Aucun pilote supplémentaire n'est nécessaire pour les fonctions de base.

---

## 🛠️ Dépannage

| Problème                        | Solution                                                         |
| ------------------------------- | ---------------------------------------------------------------- |
| Seule une moitié répond         | Vérifiez que le câble d'interconnexion est bien branché.         |
| Clavier non détecté             | Essayez un autre port USB-C ou un autre câble.                   |
| Touches lentes ou non réactives | Vérifiez toutes les connexions de câbles et redémarrez votre PC. |

---

## 🔧 Mises à jour du firmware & personnalisation du keymap

Le clavier Zero prend en charge la personnalisation avancée via **QMK** ou **VIA**.

- Personnalisez les mappages de touches, les couches et les macros.
- Téléchargez le firmware ou les outils de configuration sur : [onspry.com/firmware](https://onspry.com/firmware)

> ⚠️ _La modification du firmware est optionnelle et réservée aux utilisateurs avancés._

---

## 🎹 Personnalisation des touches avec Vial

Le clavier Zero prend en charge la personnalisation des touches via l'application **Vial**, un outil convivial pour configurer votre disposition de clavier.

### Utiliser Vial Web

1. Visitez [vial.rocks](https://vial.rocks) dans votre navigateur web
2. Connectez votre clavier à votre ordinateur
3. Le clavier doit être en mode déverrouillé pour continuer
4. Maintenez les touches spécifiées enfoncées jusqu'à ce que la barre de progression soit remplie
5. Une fois connecté, vous pouvez :
   - Réassigner les touches
   - Créer des couches personnalisées
   - Configurer des macros
   - Ajuster les paramètres du clavier

> 💡 _Astuce : Pour une meilleure expérience, utilisez les dernières versions de Chrome, Chromium ou Edge._

### Utiliser l'application Vial Desktop

Pour une utilisation hors ligne ou des fonctionnalités supplémentaires :

1. Téléchargez l'application autonome Vial depuis [get.vial.today](https://get.vial.today)
2. Installez et lancez l'application
3. Connectez votre clavier
4. Suivez le même processus de déverrouillage que la version web
5. Personnalisez votre disposition de clavier selon vos besoins

> ⚠️ _Note de sécurité : Effectuez toujours la personnalisation du clavier sur des ordinateurs de confiance. N'oubliez pas de verrouiller votre clavier après avoir effectué des modifications en sélectionnant Sécurité->Verrouiller dans le menu ou en débranchant le clavier._

---

## 🧰 Support

Besoin d'aide ou une question ?

- Visitez : **[onspry.com/support](https://onspry.com/support)**
- Email : **[<EMAIL>](mailto:<EMAIL>)**

Nous sommes là pour vous accompagner à chaque étape.

---

## 📝 Conseils finaux

- Gardez les câbles bien rangés pour éviter l'usure ou les déconnexions accidentelles.
- Évitez de brancher ou de débrancher le câble d'interconnexion pendant la frappe.
- Pour plus de confort, utilisez un repose-poignets ou des accessoires d'inclinaison pour clavier séparé.

---

Merci d'avoir choisi le **Zero Keyboard** de **Onspry** — où le confort rencontre la précision.

**Bonne frappe !**
