{"dependencies": {"@azure/identity": "^4.7.0", "@inlang/paraglide-js": "^2.0.12", "@melt-ui/svelte": "^0.86.3", "@microsoft/microsoft-graph-client": "^3.0.7", "@microsoft/microsoft-graph-types": "^2.40.0", "@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@prisma/client": "^6.7.0", "arctic": "^3.3.0", "marked": "^15.0.11"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@exodus/schemasafe": "^1.3.0", "@lucide/svelte": "^0.482.0", "@sinclair/typebox": "^0.34.33", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/adapter-netlify": "^5.0.1", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.7", "@tanstack/table-core": "^8.21.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.4", "@types/node": "^22.14.0", "@types/uuid": "^10.0.0", "@typeschema/class-validator": "^0.2.0", "@vinejs/vine": "^1.8.0", "arktype": "^2.1.20", "bits-ui": "^1.4.8", "class-validator": "^0.14.2", "clsx": "^2.1.1", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-svelte": "^8.6.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^2.46.1", "formsnap": "^2.0.0-next.1", "globals": "^15.14.0", "joi": "^17.13.3", "jsdom": "^25.0.1", "lucide-svelte": "^0.476.0", "mode-watcher": "^1.0.7", "paneforge": "^1.0.0-next.5", "prettier": "^3.4.2", "prettier-plugin-sort-json": "^4.1.1", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.7.0", "superstruct": "^2.0.2", "svelte": "^5.0.0", "svelte-check": "^4.2.1", "svelte-sonner": "^0.3.28", "sveltekit-superforms": "^2.24.1", "tailwind-merge": "^3.0.2", "tailwind-variants": "^0.2.1", "tailwindcss": "^4.1.7", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "valibot": "^0.42.1", "vaul-svelte": "^1.0.0-next.7", "vite": "^6.0.0", "vitest": "^3.0.0", "yup": "^1.6.1", "zod": "^3.24.2"}, "engines": {"node": ">=22.12.0", "npm": ">=9.8.1"}, "name": "onspry-shop", "private": true, "scripts": {"build": "prisma generate && vite build", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "dev": "vite dev", "dev:open": "vite dev -- --open", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "paraglide": "npx inlang paraglide source", "prepare": "svelte-kit sync || echo ''", "preview": "vite preview", "prisma:seed": "node --experimental-specifier-resolution=node prisma/seed.js", "test": "npm run test:unit -- --run", "test:unit": "vitest"}, "type": "module", "version": "0.0.1"}