<!doctype html>
<html lang="%lang%" dir="%paraglide.textDirection%">

<head>
	<meta charset="utf-8" />
	<!-- Primary favicon -->
	<link rel="icon" href="%sveltekit.assets%/icons/favicon.svg" type="image/svg+xml" />
	<!-- Fallback favicon for browsers that don't support SVG -->
	<link rel="icon" href="%sveltekit.assets%/icons/favicon.png" type="image/png" />
	<!-- Apple touch icon -->
	<link rel="apple-touch-icon" href="%sveltekit.assets%/icons/apple-touch-icon.png" />
	<link rel="apple-touch-icon" sizes="180x180" href="%sveltekit.assets%/icons/apple-touch-icon.png" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<!-- Improved Font loading script -->
	<script>
		// Add a class during loading
		document.documentElement.classList.add('fonts-loading');

		// Set a timeout to ensure the UI doesn't hang if fonts take too long
		const fontTimeout = setTimeout(() => {
			document.documentElement.classList.remove('fonts-loading');
			document.documentElement.classList.add('fonts-failed');
			document.documentElement.style.visibility = 'visible';
		}, 3000); // 3 second timeout

		// Check when fonts are loaded
		if ('fonts' in document) {
			document.fonts.ready
				.then(() => {
					clearTimeout(fontTimeout);
					document.documentElement.classList.remove('fonts-loading');
					document.documentElement.classList.add('fonts-loaded');
					document.documentElement.style.visibility = 'visible';

					// Force reflow to ensure all elements use the loaded fonts
					document.body && document.body.offsetHeight;
				})
				.catch(() => {
					// If font loading promise rejects
					clearTimeout(fontTimeout);
					document.documentElement.classList.remove('fonts-loading');
					document.documentElement.classList.add('fonts-failed');
					document.documentElement.style.visibility = 'visible';
				});
		} else {
			// Browser doesn't support document.fonts
			clearTimeout(fontTimeout);
			document.documentElement.classList.remove('fonts-loading');
			document.documentElement.classList.add('fonts-failed');
			document.documentElement.style.visibility = 'visible';
		}
	</script>
	%sveltekit.head%
	<script>
		// Set initial theme before page renders to prevent flickering
		const storedTheme = localStorage.getItem('theme');
		const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
		const initialTheme = storedTheme || (systemPrefersDark ? 'dark' : 'light');
		document.documentElement.classList.toggle('dark', initialTheme === 'dark');
	</script>
</head>

<body data-sveltekit-preload-data="hover" class="app-body">
	<div style="display: contents">%sveltekit.body%</div>
</body>

</html>