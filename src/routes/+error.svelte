<script lang="ts">
	let {} = $props();
	import { onMount } from 'svelte';
	import { AlertCircle, RefreshCcw } from 'lucide-svelte';
	import { Button } from '$lib/components/ui/button';
	import { localizeHref } from '$lib/paraglide/runtime';
	import * as m from '$lib/paraglide/messages';
	let contentVisible = $state(false);
	onMount(() => {
		const timer = setTimeout(() => {
			contentVisible = true;
		}, 300);
		return () => clearTimeout(timer);
	});
</script>

<div
	class="flex flex-col items-center justify-center px-4 py-24"
	class:opacity-0={!contentVisible}
	class:opacity-100={contentVisible}
>
	<div class="relative mb-6 h-24 w-24">
		<AlertCircle size={64} class="text-destructive absolute" />
		<div class="bg-muted/20 h-full w-full animate-pulse rounded-full"></div>
	</div>
	<h1 class="mb-3 text-center">{m.error_something_went_wrong()}</h1>
	<p class="text-muted-foreground mb-8 max-w-md text-center text-lg">
		{m.error_check_connection()}
	</p>
	<div class="flex flex-col gap-4 sm:flex-row">
		<Button onclick={() => window.location.reload()} class="px-8" size="lg">
			{m.error_try_again()}
			<RefreshCcw class="ml-2 h-5 w-5" />
		</Button>
		<Button href={localizeHref('/')} variant="outline" size="lg">{m.return_to_home()}</Button>
	</div>
</div>
