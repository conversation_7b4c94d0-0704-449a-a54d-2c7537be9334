import { userRepository } from '$lib/repositories/user-repository';
import {
	createPasswordResetSession,
	invalidateUserPasswordResetSessions,
	sendPasswordResetEmail,
	storePasswordResetToken,
	PASSWORD_RESET_COOKIE_NAME
} from '$lib/server/auth/password-reset';
import { generateSessionToken } from '$lib/server/auth/session';
import { fail, redirect } from '@sveltejs/kit';
import { superValidate, message } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { passwordResetSchema } from '$lib/schemas/auth';

import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async () => {
	// Create the form with Superform
	const form = await superValidate(zod(passwordResetSchema));

	return { form };
};

export const actions: Actions = {
	default: async (event) => {
		console.log('[Forgot Password] Starting password reset flow');

		// Validate the form with Superform and Zod
		const form = await superValidate(event.request, zod(passwordResetSchema));

		// Return validation errors if any
		if (!form.valid) {
			return fail(400, { form });
		}

		const { email } = form.data;

		console.log('[Forgot Password] Looking up user with email:', email);

		// First try the original method to see if user exists at all
		const userExists = await userRepository.getUserByEmail(email);
		console.log('[Forgot Password] Original method result:', userExists ? 'found' : 'not found');

		const user = await userRepository.getUserByEmailForPasswordReset(email);
		console.log('[Forgot Password] New method result:', user ? 'found' : 'not found');
		if (user) {
			console.log('[Forgot Password] User details - provider:', user.provider, 'hasPassword:', !!user.passwordHash);
		}

		if (!user) {
			console.log('[Forgot Password] User not found:', email);
			return message(form, 'Account not found', { status: 400 });
		}

		// Check if user signed up with OAuth provider (no password to reset)
		if (user.provider !== 'email') {
			console.log('[Forgot Password] OAuth user attempted password reset:', email, 'provider:', user.provider);
			const providerName = user.provider.charAt(0).toUpperCase() + user.provider.slice(1);
			return message(form, `This account was created using ${providerName}. Please sign in using ${providerName} instead of resetting a password.`, { status: 400 });
		}

		// Additional check: ensure user has a password hash
		if (!user.passwordHash) {
			console.log('[Forgot Password] User has no password hash:', email);
			return message(form, 'This account does not have a password set. Please sign in using your original authentication method.', { status: 400 });
		}

		let sessionToken;
		let session;

		try {
			console.log('[Forgot Password] Creating reset session for user:', user.id);
			await invalidateUserPasswordResetSessions(user.id);
			sessionToken = generateSessionToken();
			session = await createPasswordResetSession(sessionToken, user.id, user.email);

			console.log('[Forgot Password] Reset session created:', session.id);

			// Send the password reset email
			await sendPasswordResetEmail(session.email, session.code);

			// Set the cookie with the proper path
			storePasswordResetToken(event, sessionToken);

			// Debug cookie
			const cookieValue = event.cookies.get(PASSWORD_RESET_COOKIE_NAME);
			console.log('[Forgot Password] Cookie set:', !!cookieValue);
		} catch (error) {
			console.error('[Forgot Password] Error in reset flow:', error);
			return message(form, 'An error occurred. Please try again.', { status: 500 });
		}

		// Redirect outside the try/catch block
		console.log('[Forgot Password] Redirecting to verify email');
		throw redirect(303, '/auth/reset-password/verify-email');
	}
};
