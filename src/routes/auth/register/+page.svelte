<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';
	import { registerSchema } from '$lib/schemas/auth';
	import * as m from '$lib/paraglide/messages.js';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Eye, EyeOff } from 'lucide-svelte';
	import {
		Card,
		CardContent,
		CardDescription,
		CardFooter,
		CardHeader,
		CardTitle
	} from '$lib/components/ui/card';
	import LoadingSpinner from '$lib/components/loading-spinner.svelte';
	import PasswordStrengthIndicator from '$lib/components/password-strength-indicator.svelte';
	import { localizeHref } from '$lib/paraglide/runtime';

	// Using $props() instead of export let for Svelte 5
	let { data } = $props();

	// Initialize the form with Superform
	const { form, errors, enhance, submitting, message } = superForm(data.form, {
		validators: zod(registerSchema),
		validationMethod: 'auto'
	});

	let showPassword = $state(false);
</script>

<div
	class="container flex min-h-[calc(100vh-var(--header-height)-var(--footer-height))] items-center justify-center py-8"
>
	<Card class="w-full max-w-md">
		<CardHeader>
			<CardTitle>{m.checkout_create_account()}</CardTitle>
			<CardDescription>{m.register_description()}</CardDescription>
		</CardHeader>

		<CardContent>
			<form method="post" class="space-y-4" use:enhance>
				<div class="grid grid-cols-2 gap-4">
					<div class="grid gap-2">
						<Label for="firstName">{m.checkout_first_name()}</Label>
						<Input
							id="firstName"
							name="firstName"
							bind:value={$form.firstName}
							aria-invalid={$errors.firstName ? 'true' : undefined}
							disabled={$submitting}
						/>
						{#if $errors.firstName}
							<p class="text-destructive text-sm">{$errors.firstName}</p>
						{/if}
					</div>

					<div class="grid gap-2">
						<Label for="lastName">{m.checkout_last_name()}</Label>
						<Input
							id="lastName"
							name="lastName"
							bind:value={$form.lastName}
							aria-invalid={$errors.lastName ? 'true' : undefined}
							disabled={$submitting}
						/>
						{#if $errors.lastName}
							<p class="text-destructive text-sm">{$errors.lastName}</p>
						{/if}
					</div>
				</div>

				<div class="grid gap-2">
					<Label for="email">{m.checkout_email()}</Label>
					<Input
						type="email"
						id="email"
						name="email"
						bind:value={$form.email}
						autocomplete="email"
						aria-invalid={$errors.email ? 'true' : undefined}
						disabled={$submitting}
					/>
					{#if $errors.email}
						<p class="text-destructive text-sm">{$errors.email}</p>
					{/if}
				</div>

				<div class="grid gap-2">
					<Label for="password">{m.password()}</Label>
					<div class="relative">
						<Input
							type={showPassword ? 'text' : 'password'}
							id="password"
							name="password"
							bind:value={$form.password}
							autocomplete="new-password"
							aria-invalid={$errors.password ? 'true' : undefined}
							disabled={$submitting}
							class="pr-10"
						/>
						<button
							type="button"
							class="text-muted-foreground hover:text-foreground absolute top-1/2 right-3 -translate-y-1/2"
							onclick={() => (showPassword = !showPassword)}
							disabled={$submitting}
						>
							{#if showPassword}
								<EyeOff class="h-4 w-4" />
							{:else}
								<Eye class="h-4 w-4" />
							{/if}
						</button>
					</div>

					<!-- Password strength indicator -->
					<PasswordStrengthIndicator password={$form.password} />

					{#if $errors.password}
						<p class="text-destructive text-sm">{$errors.password}</p>
					{/if}
				</div>

				{#if $message}
					<div class="text-destructive text-sm">
						{#if typeof $message === 'string'}
							{$message}
						{:else if typeof $message === 'object'}
							{#each Object.entries($message) as [field, error]}
								<p>{error}</p>
							{/each}
						{:else}
							{m.error_during_registration()}
						{/if}
					</div>
				{/if}

				<Button type="submit" variant="default" class="w-full" disabled={$submitting}>
					{#if $submitting}
						<LoadingSpinner size={16} className="mr-2" />
					{/if}
					{m.create_account()}
				</Button>
			</form>
		</CardContent>

		<CardFooter>
			<div class="text-center text-sm">
				{m.have_account()}
				<a href={localizeHref('/auth/login')} class="hover:text-primary ml-1 font-medium"
					>{m.sign_in()}</a
				>
			</div>
		</CardFooter>
	</Card>
</div>
