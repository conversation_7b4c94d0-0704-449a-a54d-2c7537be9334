<script lang="ts">
	let { data } = $props();

	import { formatPrice } from '$lib/utils/price';
	import { formatDateTime } from '$lib/utils/date';
	import { ArrowRight } from 'lucide-svelte';
	import * as m from '$lib/paraglide/messages';
	import { Button } from '$lib/components/ui/button';

	// Loading states for each order button
	let loadingStates = $state<Record<string, boolean>>({});

	// Handle navigation with loading state
	function handleViewDetails(orderId: string) {
		loadingStates[orderId] = true;
		setTimeout(() => {
			window.location.href = `/orders/confirmation/${orderId}`;
		}, 300);
	}
</script>

<div class="mx-auto max-w-[1400px] px-4 py-8 sm:px-6 lg:px-8">
	<div class="mb-8">
		<h1>{m.orders_title ? m.orders_title() : 'My Orders'}</h1>
	</div>

	{#if data.orders.length === 0}
		<!-- Empty State -->
		<div class="flex flex-col items-center justify-center py-24">
			<div class="icon-container mb-6 h-24 w-24">
				<svg
					class="text-muted-adaptive h-8 w-8"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
					></path>
				</svg>
			</div>
			<div class="section-header text-center">
				<h2 class="page-title mb-3">No Orders Yet</h2>
				<p class="section-subtitle max-w-md">
					{m.orders_empty_message
						? m.orders_empty_message()
						: 'You have not placed any orders yet.'}
				</p>
			</div>
		</div>
	{:else}
		<!-- Orders Grid -->
		<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
			{#each data.orders as order}
				<div
					class="group card-hover bg-background hover:bg-muted/20 flex flex-col gap-4 rounded-lg p-6"
				>
					<div>
						<div class="text-lg font-semibold">
							{m.orders_order_number
								? m.orders_order_number({ number: order.orderNumber })
								: `Order #${order.orderNumber}`}
						</div>
						<span
							class="bg-primary/10 text-primary mt-2 inline-flex items-center rounded-full px-3 py-1 text-sm font-medium"
						>
							{#if order.status === 'pending_payment'}
								<svg viewBox="0 0 20 20" fill="none" stroke="currentColor" class="mr-1 h-4 w-4">
									<circle cx="10" cy="10" r="9" stroke-width="1.2" />
									<path
										d="M10 6v4l2 2"
										stroke-width="1.2"
										stroke-linecap="round"
										stroke-linejoin="round"
									/>
								</svg>
							{/if}
							<span class="capitalize">{order.status.replace('_', ' ')}</span>
						</span>
					</div>

					<div class="space-y-2">
						<div class="text-muted-foreground text-sm">
							{m.orders_placed_on
								? m.orders_placed_on({ date: formatDateTime(order.createdAt) })
								: formatDateTime(order.createdAt)}
						</div>
						<div class="text-lg font-semibold">
							{formatPrice(order.total)}
						</div>
						<div class="text-muted-foreground text-sm">
							{order.items.length}
							{m.orders_quantity ? m.orders_quantity({ quantity: order.items.length }) : 'items'}
						</div>
					</div>

					<div class="mt-auto border-t pt-4">
						<Button
							class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-full items-center justify-center gap-2 rounded-md px-4 py-2 text-sm font-medium transition-colors disabled:opacity-50"
							type="button"
							onclick={() => handleViewDetails(order.id)}
							disabled={loadingStates[order.id]}
						>
							{#if loadingStates[order.id]}
								<span
									class="border-primary-foreground block h-4 w-4 animate-spin rounded-full border-2 border-t-transparent"
								></span>
								<span class="ml-2">{m.loading()}</span>
							{:else}
								{m.orders_view_details ? m.orders_view_details() : 'View Details'}
								<ArrowRight class="ml-2 h-4 w-4" />
							{/if}
						</Button>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>
