<script lang="ts">
	// Use the data from the load function instead of page store
	import type { PageData } from './$types';

	import { formatPrice } from '$lib/utils/price';
	import { formatDateTime, formatEstimatedDelivery } from '$lib/utils/date';
	import type { SupportedCurrency } from '$lib/utils/price';
	import { Truck, ShoppingBag, ArrowRight, ArrowLeft } from 'lucide-svelte';
	import * as m from '$lib/paraglide/messages';
	import { localizeHref } from '$lib/paraglide/runtime';
	import { Button } from '$lib/components/ui/button';

	// Get the order data from the server
	let { data }: { data: PageData } = $props();
	const { order } = data;

	// Calculate estimated delivery date (7 days from order date)
	const estimatedDelivery = formatEstimatedDelivery(order.createdAt, 7);

	// Helper function to format prices with the order's currency
	function formatOrderPrice(price: number): string {
		// Map currency to locale for proper formatting
		const currencyToLocale: Record<SupportedCurrency, string> = {
			USD: 'en-US',
			EUR: 'de-DE',
			GBP: 'en-UK',
			CNY: 'zh-CN'
		};

		const locale = currencyToLocale[order.currency as SupportedCurrency] || 'en-US';
		return formatPrice(price, locale as any);
	}

	const statusOrder = ['pending_payment', 'processing', 'shipped', 'delivered'];
	const currentStep = statusOrder.indexOf(order.status);

	// Loading states for buttons
	let isNavigatingToOrders = $state(false);
	let isNavigatingToProducts = $state(false);

	// Handle navigation with loading states
	function handleViewAllOrders() {
		isNavigatingToOrders = true;
		setTimeout(() => {
			window.location.href = localizeHref('/orders/my-orders');
		}, 300);
	}

	function handleContinueShopping() {
		isNavigatingToProducts = true;
		setTimeout(() => {
			window.location.href = localizeHref('/products');
		}, 300);
	}
</script>

<div class="mx-auto max-w-[1400px] px-4 py-8 sm:px-6 lg:px-8">
	<!-- Success Header -->
	<div class="mb-8 text-center">
		<div class="bg-primary/10 mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full">
			<svg class="text-primary h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"
				></path>
			</svg>
		</div>
		<h1 class="mb-3">{m.order_confirmation_thank_you()}</h1>
		<p class="text-muted-foreground text-xl">
			{m.order_confirmation_number({ number: order.orderNumber })}
		</p>
		<p class="text-muted-foreground mt-2">
			{m.order_confirmation_email_sent()}
		</p>
	</div>

	<!-- Order Status Stepper (dynamic, bound to order.status) -->
	<div class="relative mb-8">
		<div class="bg-muted absolute top-5 left-0 hidden h-0.5 w-full sm:block"></div>
		<ol class="relative grid w-full grid-cols-4">
			{#each statusOrder as _, i}
				<li class="flex flex-col items-center">
					<div
						class="z-10 mb-2 flex h-10 w-10 items-center justify-center rounded-full border-2
							{i < currentStep ? 'border-primary bg-primary text-primary-foreground opacity-100' : ''}
							{i === currentStep
							? 'border-primary bg-primary text-primary-foreground ring-primary/50 opacity-100 ring-2'
							: ''}
							{i > currentStep ? 'border-muted bg-background text-muted-foreground opacity-60' : ''}"
					>
						{#if i < currentStep}
							<!-- Completed: checkmark -->
							<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M5 13l4 4L19 7"
								></path>
							</svg>
						{:else if i === currentStep}
							<!-- Active: step number -->
							<span class="text-sm font-bold">{i + 1}</span>
						{:else}
							<!-- Upcoming: step number -->
							<span class="text-sm font-medium">{i + 1}</span>
						{/if}
					</div>
					<span class="text-sm font-medium">
						{i === 0
							? m.order_placed()
							: i === 1
								? m.order_processing()
								: i === 2
									? m.order_estimated_delivery()
									: m.order_progress_delivered()}
					</span>
					{#if i === 0}
						<span class="text-muted-foreground text-xs">{formatDateTime(order.createdAt)}</span>
					{:else if i === 2}
						<span class="text-muted-foreground text-xs">{estimatedDelivery}</span>
					{/if}
				</li>
			{/each}
		</ol>
	</div>

	<!-- Order Details -->
	<div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
		<!-- Order Summary -->
		<div class="lg:col-span-2">
			<div class="mb-6">
				<h2 class="flex items-center gap-2">
					<ShoppingBag class="h-5 w-5" />
					{m.order_summary()}
				</h2>
			</div>
			<div class="space-y-6">
				{#each order.items as item}
					<div class="flex items-start justify-between border-b pb-4">
						<div class="flex gap-4">
							<div class="bg-muted flex h-16 w-16 items-center justify-center rounded-md">
								<ShoppingBag class="text-muted-foreground h-8 w-8" />
							</div>
							<div>
								<h3 class="font-medium">{item.name}</h3>
								<p class="text-muted-foreground text-sm">{item.variantName}</p>
								<p class="text-sm">{m.checkout_quantity()} {item.quantity}</p>

								<!-- Composite items (if any) -->
								{#if item.composites && item.composites.length > 0}
									<div class="mt-2 space-y-1">
										{#each item.composites as composite}
											<div class="flex items-center gap-1">
												<div class="bg-muted-foreground/40 h-1.5 w-1.5 rounded-full"></div>
												<p class="text-muted-foreground text-sm">
													{composite.name}
												</p>
											</div>
										{/each}
									</div>
								{/if}
							</div>
						</div>
						<div class="text-right">
							<p class="font-medium">{formatOrderPrice(item.totalPrice)}</p>
							{#if item.quantity > 1}
								<p class="text-muted-foreground text-sm">
									{formatOrderPrice(item.unitPriceWithTax)} × {item.quantity}
								</p>
							{/if}
						</div>
					</div>
				{/each}

				<!-- Price Summary -->
				<div class="space-y-2 pt-4">
					<div class="flex justify-between text-sm">
						<span class="text-muted-foreground">{m.cart_subtotal()}</span>
						<span>{formatOrderPrice(order.subtotal)}</span>
					</div>
					<div class="flex justify-between text-sm">
						<span class="text-muted-foreground">{m.cart_shipping()}</span>
						<span>{formatOrderPrice(order.shippingAmount)}</span>
					</div>

					<!-- Tax information - prominently displayed but marked as included -->
					{#if order.taxAmount > 0}
						<div class="flex justify-between text-sm">
							<span class="text-muted-foreground">
								{#if order?.taxRate}
									VAT ({(order.taxRate * 100).toFixed(1)}%) - {order.shippingAddress?.country ||
										'Unknown'} included
								{:else}
									Tax - {order.shippingAddress?.country || 'Unknown'} included
								{/if}
							</span>
							<span class="text-muted-foreground">
								{formatOrderPrice(order.taxAmount)}
							</span>
						</div>
					{/if}

					<div class="mt-4 flex justify-between border-t pt-4 text-lg font-medium">
						<span>{m.total()}</span>
						<span>{formatOrderPrice(order.total)}</span>
					</div>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="mt-8 border-t pt-6">
				<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
					<Button variant="outline" onclick={handleViewAllOrders} disabled={isNavigatingToOrders}>
						{#if isNavigatingToOrders}
							<span
								class="border-foreground block h-5 w-5 animate-spin rounded-full border-2 border-t-transparent"
							></span>
							<span class="ml-2">{m.loading()}</span>
						{:else}
							<ArrowLeft class="mr-2 h-5 w-5" />
							{m.order_view_all()}
						{/if}
					</Button>
					<Button onclick={handleContinueShopping} disabled={isNavigatingToProducts}>
						{#if isNavigatingToProducts}
							<span
								class="border-primary-foreground block h-5 w-5 animate-spin rounded-full border-2 border-t-transparent"
							></span>
							<span class="ml-2">{m.loading()}</span>
						{:else}
							{m.order_continue_shopping()}
							<ArrowRight class="ml-2 h-5 w-5" />
						{/if}
					</Button>
				</div>
			</div>
		</div>

		<!-- Shipping & Payment Info -->
		<div class="space-y-6">
			<!-- Shipping Information -->
			<div class="mb-6">
				<h2 class="flex items-center gap-2">
					<Truck class="h-5 w-5" />
					{m.order_shipping_information()}
				</h2>
			</div>
			<div class="space-y-2">
				<p class="font-medium">
					{order.shippingAddress.firstName}
					{order.shippingAddress.lastName}
				</p>
				<p>{order.shippingAddress.address1}</p>
				{#if order.shippingAddress.address2}
					<p>{order.shippingAddress.address2}</p>
				{/if}
				<p>
					{order.shippingAddress.city}, {order.shippingAddress.state}
					{order.shippingAddress.postalCode}
				</p>
				<p>{order.shippingAddress.country}</p>
				{#if order.shippingAddress.phone}
					<p>{order.shippingAddress.phone}</p>
				{/if}
			</div>
		</div>
	</div>
</div>
