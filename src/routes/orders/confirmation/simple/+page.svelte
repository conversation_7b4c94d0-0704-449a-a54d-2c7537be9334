<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { CheckCircle, ArrowRight, ArrowLeft } from 'lucide-svelte';
	import * as m from '$lib/paraglide/messages';
	import { userStore } from '$lib/stores/auth';
	import { localizeHref } from '$lib/paraglide/runtime';

	// Get user status
	const user = $derived($userStore);
</script>

<div class="mx-auto max-w-[1400px] px-4 py-8 sm:px-6 lg:px-8">
	<!-- Success Header -->
	<div class="mb-8 text-center">
		<div class="bg-primary/10 mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full">
			<CheckCircle class="text-primary h-8 w-8" />
		</div>
		<h1 class="mb-3">{m.order_confirmation_thank_you()}</h1>
		<p class="text-muted-foreground mb-6 text-xl">
			{m.order_confirmation_email_sent()}
		</p>

		<div class="mx-auto max-w-md space-y-4">
			<p class="text-muted-foreground">{m.order_email_updates()}</p>
			<p class="text-muted-foreground">
				{m.order_create_account_suggestion()}
			</p>
		</div>
	</div>

	<!-- Action Buttons -->
	<div class="mx-auto max-w-lg">
		<div class="flex justify-center">
			<Button href={localizeHref('/products')} size="lg">
				{m.order_continue_shopping()}
				<ArrowRight class="ml-2 h-4 w-4" />
			</Button>
		</div>
	</div>
</div>
