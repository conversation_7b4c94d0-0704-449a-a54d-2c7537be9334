<script lang="ts">
	import { cart } from '$lib/stores/cart';
	import CartItem from '$lib/components/cart-item.svelte';
	import { Button } from '$lib/components/ui/button';
	import { ShoppingCart, ArrowRight } from 'lucide-svelte';
	import { formatPrice, getPriceForLocale } from '$lib/utils/price';
	import { goto } from '$app/navigation';
	import * as m from '$lib/paraglide/messages';
	import { onMount } from 'svelte';
	import { localizeHref } from '$lib/paraglide/runtime';
	import type { Locale } from '$lib/utils/localization';

	import { getLocale } from '$lib/paraglide/runtime.js';
	// No need for toast import as it's handled in the cart-item component

	// Get the cart data from the server
	let { data } = $props();

	// Use the cart store directly
	$effect(() => {
		if (data?.cart) {
			cart.set(data.cart);
		}
	});

	// Calculate cart total using current locale to avoid currency mismatch issues
	const currentLocaleTotal = $derived.by(() => {
		const currentLocale = getLocale() as Locale;
		return $cart.items.reduce((sum, item) => {
			const price = getPriceForLocale(item.prices, currentLocale, 0);
			return sum + price * item.quantity;
		}, 0);
	});

	// Content visibility control
	let contentVisible = $state(false);

	// Set timeout to prevent flash of content
	onMount(() => {
		// Remove artificial delay
		contentVisible = true;
	});

	// No need for client-side cart handling functions anymore
	// We're using server actions directly in the cart-item component

	// Loading state for checkout button
	let isCheckingOut = $state(false);

	// Handle checkout button click
	function handleCheckout() {
		isCheckingOut = true;
		// Simulate a small delay to show the loading state
		setTimeout(() => {
			goto(localizeHref('/checkout'));
		}, 300);
	}
</script>

<div
	class="container-section py-8 transition-opacity duration-500"
	class:opacity-0={!contentVisible}
	class:opacity-100={contentVisible}
>
	{#if !$cart || !$cart.items || $cart.items.length === 0}
		<!-- Empty Cart View - Full Width Layout -->
		<div class="flex flex-col items-center justify-center py-24">
			<div class="icon-container mb-6 h-24 w-24">
				<ShoppingCart size={32} class="text-muted-adaptive" />
			</div>
			<div class="section-header mb-8 text-center">
				<h2>{m.cart_empty_title()}</h2>
				<p class="section-subtitle max-w-md">
					{m.cart_empty_message()}
				</p>
			</div>
			<div class="flex flex-col gap-4 sm:flex-row">
				<Button href={localizeHref('/products')} size="lg" class="btn-arrow-right px-8">
					{m.cart_browse_products()}
					<ArrowRight class="arrow-icon ml-2 h-5 w-5" />
				</Button>
				<Button href={localizeHref('/')} variant="outline" size="lg">{m.return_to_home()}</Button>
			</div>
		</div>
	{:else}
		<!-- Populated Cart View - Two Column Layout -->
		<div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
			<!-- Cart items list -->
			<div class="lg:col-span-2">
				<div class="mb-6">
					<h2 class="flex items-center gap-2">
						<ShoppingCart class="h-5 w-5" />
						{m.cart_title()}
					</h2>
				</div>

				<div class="space-y-4 sm:space-y-6">
					{#each $cart.items as item (item.id)}
						<CartItem {item} disabled={false} />
					{/each}
				</div>
			</div>

			<!-- Cart summary -->
			{#if $cart && $cart.items && $cart.items.length > 0}
				<div>
					<div class="mb-6">
						<h2 class=" flex items-center gap-2">
							<ShoppingCart class="h-5 w-5" />
							{m.cart_summary()}
						</h2>
					</div>

					<div class="space-y-6">
						<!-- Items Summary -->
						<div class="text-muted-foreground text-sm">
							<div class="mb-1 flex justify-between">
								<span
									>{$cart.items.length}
									{$cart.items.length === 1
										? m.cart_item_count_singular()
										: m.cart_item_count_plural()}</span
								>
								<span
									>{$cart.items.reduce(
										(sum: number, item: (typeof $cart.items)[0]) => sum + item.quantity,
										0
									)}
									{m.cart_units_total()}</span
								>
							</div>

							{#if $cart.items.some((item: (typeof $cart.items)[0]) => item.variant.stockStatus === 'low_stock')}
								<p class="text-warning mt-2">{m.cart_low_stock_warning()}</p>
							{/if}
						</div>

						<!-- Price Breakdown -->
						<div class="space-y-4 border-t pt-6">
							<div class="flex justify-between text-base">
								<span class="text-muted-foreground">{m.cart_tax()}</span>
								<span class="text-muted-foreground">{m.cart_calculated_at_next_step()}</span>
							</div>

							<div class="mt-4 flex justify-between border-t pt-4 text-xl font-semibold">
								<span>{m.cart_total()}</span>
								<span>{formatPrice(currentLocaleTotal, getLocale())}</span>
							</div>
						</div>

						<!-- Checkout button -->
						<div class="border-t pt-6">
							<Button
								class="btn-arrow-right w-full"
								size="lg"
								onclick={handleCheckout}
								disabled={isCheckingOut}
							>
								{#if isCheckingOut}
									<span
										class="border-primary-foreground block h-5 w-5 animate-spin rounded-full border-2 border-t-transparent"
									></span>
									<span class="ml-2">{m.loading()}</span>
								{:else}
									{m.cart_proceed_to_checkout()}
									<ArrowRight class="arrow-icon ml-2 h-4 w-4" />
								{/if}
							</Button>
							<p class="text-muted-foreground/60 mt-2 text-center text-xs">
								{m.cart_secure_transaction()}
							</p>
						</div>

						<!-- Terms Agreement -->
						<div class="text-muted-foreground/60 space-y-1.5 border-t pt-6 text-center text-xs">
							<p>{m.cart_terms_agreement()}</p>
						</div>
					</div>
				</div>
			{/if}
		</div>
	{/if}
</div>
