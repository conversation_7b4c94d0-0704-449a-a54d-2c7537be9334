import { redirect } from '@sveltejs/kit';

export const load = async ({ locals, url }) => {
	// Check if user is authenticated
	if (!locals.user) {
		throw redirect(302, `/auth/login?redirectTo=${encodeURIComponent(url.pathname)}`);
	}

	// Check if user is admin
	if (!locals.user.isAdmin) {
		// Log unauthorized admin access attempt
		console.warn(
			`Unauthorized admin access attempt by user ${locals.user.id} (${locals.user.email}) to ${url.pathname}`
		);
		throw redirect(302, '/?error=admin_access_denied');
	}

	return {
		user: locals.user,
		isAdmin: true
	};
};
