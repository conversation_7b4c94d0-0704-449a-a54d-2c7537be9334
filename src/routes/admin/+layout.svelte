<script lang="ts">
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Settings, Mail, Database, Users, ShoppingBag, BarChart3, Shield } from 'lucide-svelte';

	let { data, children } = $props();

	// Admin navigation items
	const adminNavItems = [
		{
			href: '/admin/email-testing',
			label: 'Email Testing',
			icon: Mail,
			description: 'Test all email functionality'
		},
		{
			href: '/admin/users',
			label: 'User Management',
			icon: Users,
			description: 'Manage user accounts'
		},
		{
			href: '/admin/orders',
			label: 'Order Management',
			icon: ShoppingBag,
			description: 'View and manage orders'
		},
		{
			href: '/admin/analytics',
			label: 'Analytics',
			icon: BarChart3,
			description: 'View site analytics'
		},
		{
			href: '/admin/database',
			label: 'Database Tools',
			icon: Database,
			description: 'Database management'
		}
	];

	// Check if current path matches nav item
	function isCurrentPage(href: string): boolean {
		return $page.url.pathname === href;
	}
</script>

<svelte:head>
	<title>Admin Panel - ONSPRY</title>
</svelte:head>

<div class="bg-muted/30 min-h-screen">
	<!-- Admin Header -->
	<header class="bg-background/95 supports-backdrop-filter:bg-background/60 border-b backdrop-blur">
		<div class="container mx-auto flex h-16 items-center justify-between px-4">
			<div class="flex items-center gap-4">
				<div class="flex items-center gap-2">
					<Shield class="h-6 w-6 text-orange-600" />
					<h1>Admin Panel</h1>
				</div>
				<Badge
					variant="secondary"
					class="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
				>
					Administrator
				</Badge>
			</div>

			<div class="flex items-center gap-4">
				<span class="text-muted-foreground text-sm">
					{data.user.firstName}
					{data.user.lastName}
				</span>
				<Button variant="outline" size="sm" href="/">← Back to Site</Button>
			</div>
		</div>
	</header>

	<div class="container mx-auto flex gap-6 p-6">
		<!-- Admin Sidebar Navigation -->
		<aside class="w-80 space-y-4">
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						<Settings class="h-5 w-5" />
						Admin Tools
					</CardTitle>
				</CardHeader>
				<CardContent class="space-y-2">
					{#each adminNavItems as item}
						<a
							href={item.href}
							class="hover:bg-muted flex items-start gap-3 rounded-lg p-3 transition-colors {isCurrentPage(
								item.href
							)
								? 'border border-orange-200 bg-orange-50 dark:border-orange-900 dark:bg-orange-950/30'
								: ''}"
						>
							<item.icon
								class="mt-0.5 h-5 w-5 {isCurrentPage(item.href)
									? 'text-orange-600'
									: 'text-muted-foreground'}"
							/>
							<div class="flex-1">
								<div
									class="font-medium {isCurrentPage(item.href)
										? 'text-orange-900 dark:text-orange-100'
										: ''}"
								>
									{item.label}
								</div>
								<div class="text-muted-foreground text-xs">
									{item.description}
								</div>
							</div>
						</a>
					{/each}
				</CardContent>
			</Card>

			<!-- Admin Info Card -->
			<Card class="border-blue-200 bg-blue-50/50 dark:border-blue-900 dark:bg-blue-950/20">
				<CardContent class="pt-6">
					<div class="space-y-2 text-sm">
						<div class="flex items-center gap-2 font-medium text-blue-700 dark:text-blue-300">
							<Shield class="h-4 w-4" />
							Admin Access Level
						</div>
						<ul class="space-y-1 text-xs text-blue-600 dark:text-blue-400">
							<li>• Full system access</li>
							<li>• User management</li>
							<li>• Order management</li>
							<li>• Email testing</li>
							<li>• Database tools</li>
						</ul>
					</div>
				</CardContent>
			</Card>
		</aside>

		<!-- Main Admin Content -->
		<main class="flex-1">
			{@render children()}
		</main>
	</div>
</div>
