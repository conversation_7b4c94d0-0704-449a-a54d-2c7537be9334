<script lang="ts">
	import { enhance } from '$app/forms';
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Badge } from '$lib/components/ui/badge';
	import { Separator } from '$lib/components/ui/separator';
	import {
		Mail,
		Package,
		Key,
		Lock,
		CheckCircle,
		XCircle,
		Clock,
		Send,
		AlertCircle,
		Zap
	} from 'lucide-svelte';
	import { toast } from 'svelte-sonner';

	let { data, form } = $props();

	let testEmail = $state(data.user.email || '');
	let isLoading = $state('');

	// Handle form enhancement
	function handleSubmit(actionType: string) {
		return () => {
			isLoading = actionType;

			return async ({
				result
			}: {
				result: { type: string; data?: { success?: boolean; message?: string } };
			}) => {
				isLoading = '';

				if (result.type === 'success') {
					if (result.data?.success) {
						toast.success(result.data?.message || 'Operation completed successfully');
					}
				} else if (result.type === 'failure') {
					toast.error(result.data?.message || 'An error occurred');
				}
			};
		};
	}

	// Format duration for display
	function formatDuration(ms: number): string {
		if (ms < 1000) return `${ms}ms`;
		return `${(ms / 1000).toFixed(1)}s`;
	}
</script>

<svelte:head>
	<title>Email Testing - Admin</title>
</svelte:head>

<div class="container mx-auto max-w-6xl space-y-8 p-6">
	<!-- Header -->
	<div class="space-y-2">
		<h1 class="font-bold tracking-tight">Email Testing Utility</h1>
		<p class="text-muted-foreground">
			Test all email sends without affecting production data. Logged in as {data.user.firstName}
			{data.user.lastName}
		</p>
	</div>

	<!-- Email Input Section -->
	<Card class="border-dashed">
		<CardHeader>
			<CardTitle class="flex items-center gap-2">
				<Mail class="h-5 w-5" />
				Test Email Configuration
			</CardTitle>
		</CardHeader>
		<CardContent>
			<div class="space-y-4">
				<div class="grid gap-2">
					<Label for="test-email">Test Email Address</Label>
					<Input
						id="test-email"
						type="email"
						bind:value={testEmail}
						placeholder="Enter email to receive test emails"
						class="max-w-md"
					/>
					<p class="text-muted-foreground text-sm">All test emails will be sent to this address</p>
				</div>
			</div>
		</CardContent>
	</Card>

	<!-- Quick Test All Section -->
	<Card class="border-orange-200 bg-orange-50/50 dark:border-orange-900 dark:bg-orange-950/20">
		<CardHeader>
			<CardTitle class="flex items-center gap-2 text-orange-700 dark:text-orange-300">
				<Zap class="h-5 w-5" />
				Quick Test All Emails
			</CardTitle>
		</CardHeader>
		<CardContent>
			<form
				method="POST"
				action="?/testAllEmails"
				use:enhance={handleSubmit('all')}
				class="space-y-4"
			>
				<input type="hidden" name="email" value={testEmail} />
				<div class="flex items-center gap-4">
					<Button
						type="submit"
						class="bg-orange-600 hover:bg-orange-700"
						disabled={!testEmail || isLoading === 'all'}
					>
						{#if isLoading === 'all'}
							<Clock class="mr-2 h-4 w-4 animate-spin" />
							Testing All Emails...
						{:else}
							<Send class="mr-2 h-4 w-4" />
							Test All Email Types
						{/if}
					</Button>
					<span class="text-muted-foreground text-sm">
						Sends order confirmation, verification, and password reset emails
					</span>
				</div>
			</form>

			<!-- All Emails Test Results -->
			{#if form?.results}
				<div class="mt-6 space-y-4">
					<Separator />
					<div class="space-y-3">
						<h4 class="flex items-center gap-2 font-semibold">
							<Mail class="h-4 w-4" />
							Test Results Summary
						</h4>

						<!-- Summary Stats -->
						{#if form.summary}
							<div class="flex gap-4 text-sm">
								<Badge variant="secondary">
									Total: {form.summary.total}
								</Badge>
								<Badge
									variant="default"
									class="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
								>
									Passed: {form.summary.passed}
								</Badge>
								<Badge variant="destructive">
									Failed: {form.summary.failed}
								</Badge>
								<Badge variant="outline">
									Duration: {formatDuration(form.summary.duration)}
								</Badge>
							</div>
						{/if}

						<!-- Individual Results -->
						<div class="space-y-2">
							{#each form.results as result}
								<div class="flex items-center justify-between rounded-lg border p-3">
									<div class="flex items-center gap-3">
										{#if result.success}
											<CheckCircle class="h-4 w-4 text-green-600" />
										{:else}
											<XCircle class="h-4 w-4 text-red-600" />
										{/if}
										<span class="font-medium">{result.type}</span>
									</div>
									<div class="text-muted-foreground flex items-center gap-3 text-sm">
										{#if result.success}
											<span>✓ Success</span>
											{#if result.duration}
												<span>({formatDuration(result.duration)})</span>
											{/if}
											{#if result.details}
												<span class="text-xs">
													{#if result.details.orderNumber}
														Order: {result.details.orderNumber}
													{:else if result.details.code}
														Code: {result.details.code}
													{/if}
												</span>
											{/if}
										{:else}
											<span class="text-red-600">✗ {result.error}</span>
										{/if}
									</div>
								</div>
							{/each}
						</div>
					</div>
				</div>
			{/if}
		</CardContent>
	</Card>

	<!-- Individual Email Tests -->
	<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
		<!-- Order Confirmation Email -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Package class="h-5 w-5 text-blue-600" />
					Order Confirmation
				</CardTitle>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="text-muted-foreground space-y-2 text-sm">
					<p><strong>Template:</strong> order-confirmation.html</p>
					<p><strong>Mock Data:</strong></p>
					<ul class="ml-4 space-y-1 text-xs">
						<li>• 2 items (Keyboard + Wrist Rest)</li>
						<li>• Total: $91.36</li>
						<li>• With shipping, tax, discount</li>
						<li>• Logo attachments</li>
					</ul>
				</div>

				<form method="POST" action="?/testOrderEmail" use:enhance={handleSubmit('order')}>
					<input type="hidden" name="email" value={testEmail} />
					<Button
						type="submit"
						variant="outline"
						class="w-full"
						disabled={!testEmail || isLoading === 'order'}
					>
						{#if isLoading === 'order'}
							<Clock class="mr-2 h-4 w-4 animate-spin" />
							Sending...
						{:else}
							<Send class="mr-2 h-4 w-4" />
							Test Order Email
						{/if}
					</Button>
				</form>

				{#if form?.details && 'orderNumber' in form.details}
					<div class="rounded bg-green-50 p-2 text-xs text-green-600 dark:bg-green-950/30">
						✓ Sent: Order {form.details.orderNumber} (${form.details.total}, {form.details.items} items,
						{formatDuration(form.details.duration)})
					</div>
				{/if}
			</CardContent>
		</Card>

		<!-- Email Verification -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Key class="h-5 w-5 text-green-600" />
					Email Verification
				</CardTitle>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="text-muted-foreground space-y-2 text-sm">
					<p><strong>Function:</strong> sendVerificationEmail()</p>
					<p><strong>Mock Data:</strong></p>
					<ul class="ml-4 space-y-1 text-xs">
						<li>• 8-character code</li>
						<li>• HTML template</li>
						<li>• 10-minute expiry</li>
					</ul>
				</div>

				<form
					method="POST"
					action="?/testVerificationEmail"
					use:enhance={handleSubmit('verification')}
				>
					<input type="hidden" name="email" value={testEmail} />
					<Button
						type="submit"
						variant="outline"
						class="w-full"
						disabled={!testEmail || isLoading === 'verification'}
					>
						{#if isLoading === 'verification'}
							<Clock class="mr-2 h-4 w-4 animate-spin" />
							Sending...
						{:else}
							<Send class="mr-2 h-4 w-4" />
							Test Verification
						{/if}
					</Button>
				</form>

				{#if form?.details && 'code' in form.details}
					<div class="rounded bg-green-50 p-2 text-xs text-green-600 dark:bg-green-950/30">
						✓ Sent: Code {form.details.code} ({formatDuration(form.details.duration)})
					</div>
				{/if}
			</CardContent>
		</Card>

		<!-- Password Reset -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Lock class="h-5 w-5 text-purple-600" />
					Password Reset
				</CardTitle>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="text-muted-foreground space-y-2 text-sm">
					<p><strong>Function:</strong> sendPasswordResetEmail()</p>
					<p><strong>Mock Data:</strong></p>
					<ul class="ml-4 space-y-1 text-xs">
						<li>• 8-character reset code</li>
						<li>• HTML template</li>
						<li>• Security messaging</li>
					</ul>
				</div>

				<form
					method="POST"
					action="?/testPasswordResetEmail"
					use:enhance={handleSubmit('password')}
				>
					<input type="hidden" name="email" value={testEmail} />
					<Button
						type="submit"
						variant="outline"
						class="w-full"
						disabled={!testEmail || isLoading === 'password'}
					>
						{#if isLoading === 'password'}
							<Clock class="mr-2 h-4 w-4 animate-spin" />
							Sending...
						{:else}
							<Send class="mr-2 h-4 w-4" />
							Test Password Reset
						{/if}
					</Button>
				</form>

				{#if form?.details && 'code' in form.details}
					<div class="rounded bg-green-50 p-2 text-xs text-green-600 dark:bg-green-950/30">
						✓ Sent: Code {form.details.code} ({formatDuration(form.details.duration)})
					</div>
				{/if}
			</CardContent>
		</Card>
	</div>

	<!-- Tips and Information -->
	<Card class="border-yellow-200 bg-yellow-50/50 dark:border-yellow-900 dark:bg-yellow-950/20">
		<CardHeader>
			<CardTitle class="flex items-center gap-2 text-yellow-700 dark:text-yellow-300">
				<AlertCircle class="h-5 w-5" />
				Testing Tips
			</CardTitle>
		</CardHeader>
		<CardContent class="space-y-2 text-sm">
			<ul class="space-y-1">
				<li>• <strong>Check your email inbox</strong> (including spam/junk folder)</li>
				<li>
					• <strong>Verify MS Graph credentials</strong> are configured in environment variables
				</li>
				<li>• <strong>Email templates</strong> are loaded from src/lib/server/email/templates/</li>
				<li>• <strong>Attachments</strong> include logo files from static/ directory</li>
				<li>• <strong>Mock data</strong> uses realistic order information for testing</li>
			</ul>
		</CardContent>
	</Card>

	<!-- Error Display -->
	{#if form?.message && !form.success}
		<Card class="border-red-200 bg-red-50/50 dark:border-red-900 dark:bg-red-950/20">
			<CardContent class="pt-6">
				<div class="flex items-center gap-2 text-red-700 dark:text-red-300">
					<XCircle class="h-5 w-5" />
					<span class="font-medium">Error:</span>
					<span>{form.message}</span>
				</div>
			</CardContent>
		</Card>
	{/if}
</div>
