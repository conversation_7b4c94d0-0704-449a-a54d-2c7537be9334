import { redirect, fail, type Actions } from '@sveltejs/kit';
import type { OrderViewModel } from '$lib/models/order';
import { sendOrderConfirmationEmail } from '$lib/server/email/order-confirmation';
import { sendVerificationEmail } from '$lib/server/auth/email-verification';
import { sendPasswordResetEmail } from '$lib/server/auth/password-reset';

export const load = async ({ locals }: { locals: any }) => {
	// Check if user is authenticated and is admin
	if (!locals.user || !locals.user.isAdmin) {
		throw redirect(302, '/auth/login');
	}

	return {
		user: locals.user
	};
};

/**
 * Create mock order data for testing order confirmation email
 */
function createMockOrder(testEmail: string): OrderViewModel {
	const now = new Date().toISOString();
	const orderNumber = `TEST-${Date.now()}`;

	return {
		id: `test-order-${Date.now()}`,
		orderNumber,
		status: 'pending_payment',
		createdAt: now,
		total: 91.36,
		subtotal: 79.97,
		discountAmount: 5.0,
		shippingAmount: 9.99,
		taxAmount: 6.4,
		currency: 'USD',
		shippingMethod: 'standard',
		paymentMethod: 'credit_card',
		items: [
			{
				id: 'test-item-1',
				productId: 'test-product-1',
				variantId: 'test-variant-1',
				quantity: 2,
				unitPrice: 29.99,
				totalPrice: 59.98,
				name: 'Ergonomic Keyboard',
				variantName: 'Black - US Layout',
				composites: [
					{
						variantId: 'test-composite-1',
						name: 'USB-C Cable',
						quantity: 1
					}
				]
			},
			{
				id: 'test-item-2',
				productId: 'test-product-2',
				variantId: 'test-variant-2',
				quantity: 1,
				unitPrice: 19.99,
				totalPrice: 19.99,
				name: 'Wrist Rest',
				variantName: 'Memory Foam'
			}
		],
		shippingAddress: {
			firstName: 'John',
			lastName: 'Doe',
			email: testEmail,
			address1: '123 Test Street',
			address2: 'Apt 4B',
			city: 'Test City',
			state: 'CA',
			postalCode: '90210',
			country: 'United States',
			phone: '+****************'
		}
	};
}

/**
 * Generate a test verification code
 */
function generateTestCode(): string {
	return Math.random().toString(36).substr(2, 8).toUpperCase();
}

export const actions: Actions = {
	testOrderEmail: async ({ request, locals }) => {
		// Double-check admin access
		if (!locals.user?.isAdmin) {
			return fail(403, { message: 'Admin access required' });
		}

		const formData = await request.formData();
		const testEmail = formData.get('email') as string;

		if (!testEmail || !testEmail.includes('@')) {
			return fail(400, { message: 'Valid email address required' });
		}

		try {
			const start = Date.now();
			const mockOrder = createMockOrder(testEmail);

			await sendOrderConfirmationEmail(mockOrder);

			const duration = Date.now() - start;

			return {
				success: true,
				message: `Order confirmation email sent successfully to ${testEmail}`,
				details: {
					orderNumber: mockOrder.orderNumber,
					total: mockOrder.total,
					items: mockOrder.items.length,
					duration
				}
			};
		} catch (error) {
			console.error('Order email test failed:', error);
			return fail(500, {
				message: `Failed to send order confirmation email: ${error instanceof Error ? error.message : 'Unknown error'}`
			});
		}
	},

	testVerificationEmail: async ({ request, locals }) => {
		// Double-check admin access
		if (!locals.user?.isAdmin) {
			return fail(403, { message: 'Admin access required' });
		}

		const formData = await request.formData();
		const testEmail = formData.get('email') as string;

		if (!testEmail || !testEmail.includes('@')) {
			return fail(400, { message: 'Valid email address required' });
		}

		try {
			const start = Date.now();
			const testCode = generateTestCode();

			await sendVerificationEmail(testEmail, testCode);

			const duration = Date.now() - start;

			return {
				success: true,
				message: `Email verification code sent successfully to ${testEmail}`,
				details: {
					code: testCode,
					duration
				}
			};
		} catch (error) {
			console.error('Verification email test failed:', error);
			return fail(500, {
				message: `Failed to send verification email: ${error instanceof Error ? error.message : 'Unknown error'}`
			});
		}
	},

	testPasswordResetEmail: async ({ request, locals }) => {
		// Double-check admin access
		if (!locals.user?.isAdmin) {
			return fail(403, { message: 'Admin access required' });
		}

		const formData = await request.formData();
		const testEmail = formData.get('email') as string;

		if (!testEmail || !testEmail.includes('@')) {
			return fail(400, { message: 'Valid email address required' });
		}

		try {
			const start = Date.now();
			const testCode = generateTestCode();

			await sendPasswordResetEmail(testEmail, testCode);

			const duration = Date.now() - start;

			return {
				success: true,
				message: `Password reset email sent successfully to ${testEmail}`,
				details: {
					code: testCode,
					duration
				}
			};
		} catch (error) {
			console.error('Password reset email test failed:', error);
			return fail(500, {
				message: `Failed to send password reset email: ${error instanceof Error ? error.message : 'Unknown error'}`
			});
		}
	},

	testAllEmails: async ({ request, locals }) => {
		// Double-check admin access
		if (!locals.user?.isAdmin) {
			return fail(403, { message: 'Admin access required' });
		}

		const formData = await request.formData();
		const testEmail = formData.get('email') as string;

		if (!testEmail || !testEmail.includes('@')) {
			return fail(400, { message: 'Valid email address required' });
		}

		const results = [];
		const totalStart = Date.now();

		try {
			// Test order confirmation
			try {
				const start = Date.now();
				const mockOrder = createMockOrder(testEmail);
				await sendOrderConfirmationEmail(mockOrder);
				const duration = Date.now() - start;

				results.push({
					type: 'Order Confirmation',
					success: true,
					duration,
					details: {
						orderNumber: mockOrder.orderNumber,
						total: mockOrder.total,
						items: mockOrder.items.length
					}
				});
			} catch (error) {
				results.push({
					type: 'Order Confirmation',
					success: false,
					error: error instanceof Error ? error.message : 'Unknown error'
				});
			}

			// Test email verification
			try {
				const start = Date.now();
				const testCode = generateTestCode();
				await sendVerificationEmail(testEmail, testCode);
				const duration = Date.now() - start;

				results.push({
					type: 'Email Verification',
					success: true,
					duration,
					details: { code: testCode }
				});
			} catch (error) {
				results.push({
					type: 'Email Verification',
					success: false,
					error: error instanceof Error ? error.message : 'Unknown error'
				});
			}

			// Test password reset
			try {
				const start = Date.now();
				const testCode = generateTestCode();
				await sendPasswordResetEmail(testEmail, testCode);
				const duration = Date.now() - start;

				results.push({
					type: 'Password Reset',
					success: true,
					duration,
					details: { code: testCode }
				});
			} catch (error) {
				results.push({
					type: 'Password Reset',
					success: false,
					error: error instanceof Error ? error.message : 'Unknown error'
				});
			}

			const totalDuration = Date.now() - totalStart;
			const successCount = results.filter((r) => r.success).length;
			const totalCount = results.length;

			return {
				success: true,
				message: `Completed testing all email types: ${successCount}/${totalCount} successful`,
				results,
				summary: {
					total: totalCount,
					passed: successCount,
					failed: totalCount - successCount,
					duration: totalDuration
				}
			};
		} catch (error) {
			console.error('All emails test failed:', error);
			return fail(500, {
				message: `Failed to complete email testing: ${error instanceof Error ? error.message : 'Unknown error'}`,
				results
			});
		}
	}
};
