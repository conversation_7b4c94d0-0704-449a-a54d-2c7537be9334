<script lang="ts">
	// Import the localization wrapper and i18n instance.
	import '../app.css';
	// Import global components.
	import Footer from '$lib/components/footer.svelte';
	import Navbar from '$lib/components/navbar.svelte';
	import { onMount } from 'svelte';
	import { setUser } from '$lib/stores/auth';
	import { theme } from '$lib/stores/theme';
	import { Toaster } from '$lib/components/ui/sonner';
	import { cart } from '$lib/stores/cart';
	import { checkoutStore } from '$lib/stores/checkout';
	import { afterNavigate, beforeNavigate } from '$app/navigation';
	import { browser } from '$app/environment';
	import { setLocale, getLocale } from '$lib/paraglide/runtime.js';
	import { invalidate } from '$app/navigation';

	let { children, data } = $props();
	let cartInitialized = $state(false);

	// Type for available locales
	type AvailableLocale = 'en-US' | 'en-UK' | 'de-DE' | 'fr-FR' | 'zh-CN';

	// Initialize locale immediately from server data for SSR
	if (data.paraglide?.lang) {
		try {
			setLocale(data.paraglide.lang as AvailableLocale);
		} catch (error) {
			console.error('Error setting initial locale during SSR:', error);
			// Force fallback to 'en'
			setLocale('en-US');
		}
	}

	// Handle locale sync during navigation
	afterNavigate(() => {
		if (browser) {
			try {
				// Get the current URL path segments
				const pathSegments = window.location.pathname.split('/').filter(Boolean);
				const urlLocale = pathSegments[0];

				// Check if the first segment is a valid locale
				if (urlLocale && ['en', 'de', 'fr', 'cn'].includes(urlLocale)) {
					const currentLocale = getLocale();

					// If URL locale doesn't match current locale, update it
					if (currentLocale !== urlLocale) {
						console.log(`Syncing locale to match URL: ${urlLocale}`);
						setLocale(urlLocale as AvailableLocale);
						// Invalidate all data to ensure it's reloaded with new locale
						invalidate('app:locale');
					}
				}
			} catch (error) {
				console.error('Error syncing locale during navigation:', error);
			}
		}
	});

	// Initialize browser-specific settings on mount
	onMount(() => {
		if (browser) {
			try {
				// Set theme
				const isDark = $theme === 'dark';
				document.documentElement.classList.toggle('dark', isDark);

				// Set HTML lang attribute (Paraglide handles the actual locale)
				document.documentElement.lang = getLocale();

				// Sync cookie with URL language on initial page load (OOTB approach)
				// This ensures if someone lands on /de/products, the cookie gets set to 'de'
				if (window.location.pathname) {
					const pathSegments = window.location.pathname.split('/').filter(Boolean);
					const firstSegment = pathSegments[0];

					// If URL starts with a valid locale, ensure the cookie matches
					if (firstSegment && ['en', 'de', 'fr', 'cn'].includes(firstSegment)) {
						// Get current locale from cookie/state
						const currentLocale = getLocale();

						// Only update if they don't match (URL has locale but cookie doesn't match)
						if (currentLocale !== firstSegment) {
							console.log(`Setting locale cookie to match URL: ${firstSegment}`);
							setLocale(firstSegment as 'en-US' | 'en-UK' | 'de-DE' | 'fr-FR' | 'zh-CN' | 'zh-CN');
						}
					}
				}
			} catch (error) {
				console.error('Error in layout onMount:', error);
			}
		}
	});

	// Update HTML lang attribute when the page loads
	$effect(() => {
		if (browser && document.documentElement) {
			try {
				const currentLang = getLocale();
				document.documentElement.lang = currentLang;
			} catch (error) {
				console.error('Error updating HTML lang attribute:', error);
				document.documentElement.lang = 'en';
			}
		}
	});

	// Update context when user changes
	$effect(() => {
		// Set user data from server (can be null after database reset)
		setUser(data.user || null);
	});

	// Hydrate cart store ONLY on first load
	$effect(() => {
		try {
			console.log('Cart effect:', { cartInitialized, cart: data.cart });
			if (!cartInitialized) {
				if (data.cart && typeof data.cart === 'object') {
					console.log('Setting cart:', data.cart);
					cart.set(data.cart);
				} else {
					console.log('Clearing cart');
					cart.clear();
				}
				cartInitialized = true;
			}
		} catch (error) {
			console.error('Cart effect error:', error);
			cartInitialized = true;
		}
	});

	// Handle checkout store reset
	beforeNavigate(({ to }) => {
		// Reset checkout store before navigating to logout
		if (to?.url.pathname === '/auth/logout') {
			console.log('Navigating to logout page, resetting checkout store');
			checkoutStore.reset();

			// Force clear localStorage directly as a backup
			if (browser) {
				try {
					localStorage.removeItem('checkout_data');
					console.log('Directly removed checkout data from localStorage');
				} catch (error) {
					console.error('Failed to remove checkout data from localStorage:', error);
				}
			}
		}
	});

	// Handle user state after navigation
	afterNavigate(({ from }) => {
		// Check for the X-Clear-Checkout header in the response
		if (from?.url.pathname === '/auth/logout') {
			console.log('Coming from logout page, resetting checkout store and user');
			checkoutStore.reset();
			setUser(null);

			// Force clear localStorage directly as a backup
			if (browser) {
				try {
					localStorage.removeItem('checkout_data');
					console.log('Directly removed checkout data from localStorage after logout');
				} catch (error) {
					console.error('Failed to remove checkout data from localStorage after logout:', error);
				}
			}
		}
	});
</script>

<div class="bg-background flex min-h-screen w-full flex-col font-sans antialiased">
	<header
		class="bg-background/80 sticky top-0 z-50 h-(--header-height) w-full border-b backdrop-blur-sm"
	>
		<div class="mx-auto h-full w-full max-w-[1400px] px-4 sm:px-6 md:px-8 lg:px-12">
			<Navbar />
		</div>
	</header>

	<main class="w-full flex-1">
		<div class="mx-auto h-full w-full max-w-[1400px] px-4 py-4 sm:px-6 md:px-8 lg:px-12">
			{@render children()}
		</div>
	</main>

	<footer class="bg-background/80 h-(--footer-height) w-full border-t backdrop-blur-sm">
		<div class="mx-auto h-full w-full max-w-[1400px] px-4 sm:px-6 md:px-8 lg:px-12">
			<Footer />
		</div>
	</footer>
</div>

<!-- Toast positioned above footer on the right side -->
<Toaster
	richColors
	position="bottom-right"
	toastOptions={{
		classes: {
			success: 'toast-success',
			error: 'toast-error',
			actionButton: 'toast-action'
		},
		duration: 8000 // Increased from 4000 to 8000 ms
	}}
/>
