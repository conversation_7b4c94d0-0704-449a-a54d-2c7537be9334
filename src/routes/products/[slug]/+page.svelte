<script lang="ts">
	import type { PageData } from './$types';
	import type { ProductVariantViewModel } from '$lib/models/product';
	import { getPriceForLocale } from '$lib/utils/price';
	import ProductDetailKeyboard from '$lib/components/product-detail-keyboard.svelte';
	import ProductDetailAccessory from '$lib/components/product-detail-accessory.svelte';
	import { onMount } from 'svelte';
	import * as m from '$lib/paraglide/messages';
	import { invalidate } from '$app/navigation';
	import { getLocale } from '$lib/paraglide/runtime.js';

	let { data } = $props<{ data: PageData }>();

	// Content visibility control
	let contentVisible = $state(false);

	// Set timeout to prevent flash of content
	onMount(() => {
		const timer = setTimeout(() => {
			contentVisible = true;
		}, 300);

		// Add structured data safely
		if (data?.product && structuredData()) {
			const existingScript = document.querySelector('script[type="application/ld+json"]');
			if (existingScript) {
				existingScript.remove();
			}

			const script = document.createElement('script');
			script.type = 'application/ld+json';
			script.textContent = JSON.stringify(structuredData());
			document.head.appendChild(script);
		}

		return () => {
			clearTimeout(timer);
			// Clean up structured data script on unmount
			const script = document.querySelector('script[type="application/ld+json"]');
			if (script) {
				script.remove();
			}
		};
	});

	// Derive product category safely
	const productCategory = $derived(data?.product?.category?.toUpperCase() ?? '');

	// Handle variant selection
	function handleVariantSelect(event: CustomEvent<{ variantId: string }>) {
		// No need to update URL, just pass the event to the component
		console.log('Variant selected:', event.detail.variantId);
	}

	// Generate structured data for the product
	const structuredData = $derived(() => {
		if (!data?.product) return null;

		const product = data.product;
		const variants = data.variants || [];
		const images = data.images || [];
		const locale = getLocale();
		const basePrice =
			variants.length > 0
				? Math.min(
						...variants.map((v: ProductVariantViewModel) => getPriceForLocale(v.prices, locale, 0))
					)
				: 0;

		return {
			'@context': 'https://schema.org/',
			'@type': 'Product',
			name: product.name,
			description: product.description,
			image: images?.[0]?.url,
			offers: {
				'@type': 'AggregateOffer',
				priceCurrency: 'USD',
				lowPrice: basePrice,
				highPrice: Math.max(
					...variants.map((v: ProductVariantViewModel) => getPriceForLocale(v.prices, locale, 0))
				),
				availability: variants.some((v: ProductVariantViewModel) => v.stockStatus === 'in_stock')
					? 'https://schema.org/InStock'
					: 'https://schema.org/OutOfStock'
			}
		};
	});

	// Get the store name from the messages
	const storeName = m.shop_title();

	// Watch for locale changes and reload data
	let currentLocale = $state(getLocale());

	$effect(() => {
		const newLocale = getLocale();
		if (newLocale !== currentLocale) {
			console.log(`Locale changed from ${currentLocale} to ${newLocale}, invalidating data`);
			currentLocale = newLocale;
			invalidate('app:locale');
		}
	});
</script>

<!-- Add SEO metadata to head -->
<svelte:head>
	{#if data?.product}
		<title>{data.product.name} - {storeName}</title>
		<meta name="description" content={data.product.description} />
		<meta property="og:title" content={data.product.name} />
		<meta property="og:description" content={data.product.description} />
		{#if data.images?.[0]?.url}
			<meta property="og:image" content={data.images[0].url} />
		{/if}
		<link rel="canonical" href={`/products/${data.product.slug}`} />
	{/if}
</svelte:head>

<div class="bg-adaptive min-h-screen">
	<div
		class="container-section transition-opacity duration-500"
		class:opacity-0={!contentVisible}
		class:opacity-100={contentVisible}
	>
		{#if !data}
			<div class="flex h-64 items-center justify-center">
				<div class="border-primary h-12 w-12 animate-spin rounded-full border-t-2 border-b-2"></div>
			</div>
		{:else if productCategory === 'KEYBOARD'}
			<ProductDetailKeyboard
				product={data.product}
				variants={data.variants}
				images={data.images}
				switches={data.switches}
				keycaps={data.keycaps}
				searchParams={data.searchParams}
				onvariantselect={handleVariantSelect}
			/>
		{:else}
			<ProductDetailAccessory
				product={data.product}
				variants={data.variants}
				images={data.images}
			/>
		{/if}
	</div>
</div>
