<script lang="ts">
	import * as m from '$lib/paraglide/messages';
	import { Button } from '$lib/components/ui/button';
	import { <PERSON>R<PERSON>, MousePointerClick, Palette, Leaf } from 'lucide-svelte';
	import ProductCard from '$lib/components/product-card.svelte';
	import type { ProductViewModel } from '$lib/models/product';
	import { localizeHref } from '$lib/paraglide/runtime';

	let { data } = $props<{ data: { products: ProductViewModel[] } }>();
	let products = data.products;

	// Image loading state
	let heroImageLoaded = $state(false);

	function handleImageLoad() {
		heroImageLoaded = true;
	}
</script>

<!-- Preload critical hero image -->
<svelte:head>
	<link rel="preload" href="/images/products/zero/angle_main.png" as="image" />
</svelte:head>

<!-- Hero Section -->
<section
	class="relative flex flex-col items-center justify-center bg-white px-4 pt-8 text-center dark:bg-neutral-950"
>
	<div
		class="relative z-10 flex flex-col items-center justify-center px-4 py-12 text-center md:py-20"
	>
		<div
			class="animate-fade-in font-semi-bold mb-3 text-4xl tracking-tight md:text-5xl lg:text-6xl xl:text-7xl"
		>
			{m.hero_title()}
		</div>
		<p
			class="animate-fade-in text-foreground/80 mx-auto mb-8 max-w-2xl text-lg [animation-delay:200ms] md:text-xl"
		>
			{m.hero_description()}
		</p>
		<div class="animate-fade-in flex flex-col gap-3 [animation-delay:400ms] sm:flex-row">
			<Button href={localizeHref('/products')} size="lg" class="px-8">
				{m.hero_cta_shop()}
				<ArrowRight class="ml-2 h-5 w-5" />
			</Button>
			<Button href={localizeHref('/about')} variant="outline" size="lg">{m.hero_cta_learn()}</Button
			>
		</div>
	</div>
	<div class="relative w-full">
		<div
			class="to-background/20 dark:to-background/40 absolute inset-0 bg-linear-to-b from-transparent"
		></div>
		<div class="relative mx-auto -mt-24 w-full max-w-6xl" style="aspect-ratio: 2 / 1;">
			<img
				src="/images/products/zero/angle_main.png"
				alt="Featured Keyboard"
				class="absolute inset-0 h-full w-full rounded-lg object-contain shadow-lg transition-all duration-300 hover:scale-[1.02] {heroImageLoaded
					? 'opacity-100'
					: 'opacity-0'}"
				loading="eager"
				onload={handleImageLoad}
			/>
			<!-- Loading placeholder -->
			{#if !heroImageLoaded}
				<div class="bg-muted/20 absolute inset-0 animate-pulse rounded-lg"></div>
			{/if}
		</div>
	</div>
</section>

<!-- Features Section -->
<section class="bg-muted/50 dark:bg-background relative py-12">
	<div
		class="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] bg-size-[16px_16px] dark:bg-[radial-gradient(#404040_1px,transparent_1px)]"
	></div>
	<div class="relative mx-auto max-w-(--breakpoint-xl) px-4 sm:px-6 lg:px-8">
		<div class="mb-8 text-center">
			<h2>{m.features_title()}</h2>
			<p class="text-muted-foreground mx-auto mt-3 max-w-xl">
				{m.features_subtitle()}
			</p>
		</div>

		<div class="grid gap-6 md:grid-cols-3">
			<div
				class="group border-muted bg-background/80 dark:bg-background/80 flex flex-col items-center rounded-lg border p-5 shadow backdrop-blur-sm transition-all hover:-translate-y-1 hover:border-orange-200 hover:shadow-lg dark:hover:border-orange-900"
			>
				<div
					class="text-primary mb-3 flex h-10 w-10 items-center justify-center rounded-full bg-orange-100 transition-colors group-hover:bg-orange-200 dark:bg-orange-900 dark:group-hover:bg-orange-800"
				>
					<MousePointerClick class="h-5 w-5" />
				</div>
				<h3 class="mb-2">{m.feature_ergonomic_title()}</h3>
				<p class="text-muted-foreground text-center text-sm">
					{m.feature_ergonomic_desc()}
				</p>
			</div>

			<div
				class="group border-muted bg-background/80 dark:bg-background/80 flex flex-col items-center rounded-lg border p-5 shadow backdrop-blur-sm transition-all hover:-translate-y-1 hover:border-orange-200 hover:shadow-lg dark:hover:border-orange-900"
			>
				<div
					class="text-primary mb-3 flex h-10 w-10 items-center justify-center rounded-full bg-orange-100 transition-colors group-hover:bg-orange-200 dark:bg-orange-900 dark:group-hover:bg-orange-800"
				>
					<Palette class="h-5 w-5" />
				</div>
				<h3 class="mb-2">{m.feature_customizable_title()}</h3>
				<p class="text-muted-foreground text-center text-sm">
					{m.feature_customizable_desc()}
				</p>
			</div>

			<div
				class="group border-muted bg-background/80 dark:bg-background/80 flex flex-col items-center rounded-lg border p-5 shadow backdrop-blur-sm transition-all hover:-translate-y-1 hover:border-orange-200 hover:shadow-lg dark:hover:border-orange-900"
			>
				<div
					class="text-primary mb-3 flex h-10 w-10 items-center justify-center rounded-full bg-orange-100 transition-colors group-hover:bg-orange-200 dark:bg-orange-900 dark:group-hover:bg-orange-800"
				>
					<Leaf class="h-5 w-5" />
				</div>
				<h3 class="mb-2">{m.feature_sustainable_title()}</h3>
				<p class="text-muted-foreground text-center text-sm">
					{m.feature_sustainable_desc()}
				</p>
			</div>
		</div>
	</div>
</section>

<!-- Testimonials Section -->
<section class="bg-muted/30 dark:bg-background/50 py-12">
	<div class="mx-auto max-w-(--breakpoint-xl) px-4 sm:px-6 lg:px-8">
		<div class="mb-8 text-center">
			<h2>{m.testimonial_title()}</h2>
			<p class="text-muted-foreground mx-auto mt-3 max-w-xl">
				{m.testimonial_subtitle()}
			</p>
		</div>

		<div class="mx-auto max-w-2xl">
			<div
				class="group border-muted bg-background rounded-lg border p-6 shadow transition-all hover:shadow-lg"
			>
				<div class="mb-4 flex items-center">
					<div
						class="mr-4 h-12 w-12 overflow-hidden rounded-full ring-2 ring-orange-200 transition-all group-hover:ring-orange-300 dark:ring-orange-800 dark:group-hover:ring-orange-700"
					>
						<img
							src="/images/testimonials/desmond.jpg"
							alt="Desmond KH Ip"
							class="h-full w-full object-cover"
						/>
					</div>
					<div>
						<h3>Desmond KH Ip</h3>
						<p class="text-muted-foreground text-sm">Software Developer</p>
					</div>
				</div>
				<div class="relative">
					<svg
						class="absolute -top-2 -left-2 h-8 w-8 text-orange-200/50 dark:text-orange-800/50"
						viewBox="0 0 24 24"
						fill="currentColor"
					>
						<path
							d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"
						/>
					</svg>
					<blockquote class="text-muted-foreground relative pl-6 text-base">
						"Switching to a split ergonomic keyboard drastically reduced the wrist and shoulder pain
						I experienced from years of heavy typing. While the transition had a learning curve, the
						long-term comfort and posture improvements made it absolutely worth it. I now type more
						mindfully and with significantly less strain."
					</blockquote>
				</div>
				<div class="mt-4 text-right">
					<a
						href="https://medium.com/@desmond-kh-ip/adopting-a-split-ergonomic-keyboard-to-reduce-keyboard-related-injuries-part-i-cf075e953dfe"
						target="_blank"
						rel="noopener noreferrer"
						class="text-primary inline-flex items-center text-sm transition-colors hover:text-orange-600 dark:hover:text-orange-400"
					>
						{m.testimonial_read_more()}
						<ArrowRight class="ml-1 h-4 w-4" />
					</a>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Featured Products -->
<section class="py-12">
	<div class="mx-auto max-w-(--breakpoint-xl)">
		<h2 class="mb-3 text-center">{m.featured_products_title()}</h2>
		<p class="text-muted-foreground mx-auto mb-8 max-w-xl text-center">
			{m.featured_products_subtitle()}
		</p>

		<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
			{#each products as product}
				<ProductCard {product} />
			{/each}
		</div>
	</div>
</section>

<style>
	/* Hide scrollbars for horizontal carousel on mobile */
	::-webkit-scrollbar {
		display: none;
	}

	@keyframes fade-in {
		from {
			opacity: 0;
			transform: translateY(10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.animate-fade-in {
		animation: fade-in 0.5s ease-out forwards;
	}
</style>
