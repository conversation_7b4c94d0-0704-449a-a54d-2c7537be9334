import { error, redirect } from '@sveltejs/kit';
import type { <PERSON>questHandler } from './$types';

/**
 * Image server route handler for Netlify
 *
 * This handler redirects image requests to the static assets directory
 * optimized for Netlify's static serving
 */
export const GET: RequestHandler = async ({ params }) => {
	try {
		// Get the path from the URL
		const imagePath = params.path;
		if (!imagePath) {
			throw error(404, 'Image not found');
		}

		// For Netlify, we want to serve images directly from the static directory
		// without complex redirects that might cause caching issues
		const staticUrl = `/${imagePath}`;

		// Use a 301 permanent redirect for better caching
		// and remove query parameters that might interfere with Netlify's CDN
		return redirect(301, staticUrl);
	} catch (err) {
		console.error('Error handling image request:', err);
		throw error(500, 'Error handling image request');
	}
};
