/* Font loading strategy - Local fonts for better performance */
@font-face {
	font-family: 'Inter';
	font-style: normal;
	font-weight: 400;
	font-display: swap;
	src: url('/fonts/inter-v18-latin-regular.woff2') format('woff2');
}

@font-face {
	font-family: 'Onest';
	font-style: normal;
	font-weight: 400;
	font-display: swap;
	src: url('/fonts/onest-v6-latin-regular.woff2') format('woff2');
}

/* Tailwind CSS */
@import 'tailwindcss';
@import 'tw-animate-css';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

@custom-variant dark (&:is(.dark *));

:root {
	--radius: 0.625rem;

	/* Layout dimensions */
	--header-height: 4rem; /* 64px - appealing navbar height */
	--footer-height: 3.5rem; /* 56px - appealing footer height */

	/* ONSPRY Brand Colors converted to OKLCH */
	/* ONSPRY Orange #FF6B00 */
	--onspry-orange: oklch(0.72 0.18 45);
	--onspry-orange-hsl: 25 100% 50%;
	/* Sienna #832400 */
	--onspry-sienna: oklch(0.35 0.12 45);
	/* Pure White #FFFFFF */
	--onspry-white: oklch(1 0 0);
	/* Light Grey #FAFAFA */
	--onspry-light-grey: oklch(0.98 0 0);
	/* Dark Grey #9A9A9A */
	--onspry-dark-grey: oklch(0.65 0 0);
	/* Black #111111 */
	--onspry-black: oklch(0.15 0 0);

	/* Logo configuration */
	--logo: url('/logo-dark.svg') no-repeat; /* Dark text for light mode */

	/* shadcn-svelte semantic colors using ONSPRY brand */
	--background: var(--onspry-white);
	--foreground: var(--onspry-black);
	--card: var(--onspry-white);
	--card-foreground: var(--onspry-black);
	--popover: var(--onspry-white);
	--popover-foreground: var(--onspry-black);
	--primary: var(--onspry-orange);
	--primary-foreground: var(--onspry-white);
	--secondary: var(--onspry-light-grey);
	--secondary-foreground: var(--onspry-black);
	--muted: var(--onspry-light-grey);
	--muted-foreground: var(--onspry-dark-grey);
	--accent: var(--onspry-light-grey);
	--accent-foreground: var(--onspry-black);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.91 0.013 220);
	--input: oklch(0.922 0 0);
	--ring: var(--onspry-orange);
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0 0);
	--sidebar-foreground: var(--onspry-black);
	--sidebar-primary: var(--onspry-orange);
	--sidebar-primary-foreground: var(--onspry-white);
	--sidebar-accent: var(--onspry-light-grey);
	--sidebar-accent-foreground: var(--onspry-black);
	--sidebar-border: oklch(0.922 0 0);
	--sidebar-ring: var(--onspry-orange);
}

.dark {
	/* Dark mode using ONSPRY colors with shadcn-svelte structure */
	--background: var(--onspry-black);
	--foreground: var(--onspry-white);
	--logo: url('/logo-light.svg') no-repeat; /* White text for dark mode */
	--card: oklch(0.205 0 0);
	--card-foreground: var(--onspry-white);
	--popover: oklch(0.205 0 0);
	--popover-foreground: var(--onspry-white);
	--primary: var(--onspry-orange);
	--primary-foreground: var(--onspry-white);
	--secondary: oklch(0.269 0 0);
	--secondary-foreground: var(--onspry-white);
	--muted: oklch(0.269 0 0);
	--muted-foreground: oklch(0.708 0 0);
	--accent: oklch(0.269 0 0);
	--accent-foreground: var(--onspry-white);
	--destructive: oklch(0.704 0.191 22.216);
	--border: oklch(0.3 0.008 240);
	--input: oklch(1 0 0 / 15%);
	--ring: var(--onspry-orange);
	--chart-1: oklch(0.488 0.243 264.376);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.205 0 0);
	--sidebar-foreground: var(--onspry-white);
	--sidebar-primary: var(--onspry-orange);
	--sidebar-primary-foreground: var(--onspry-white);
	--sidebar-accent: oklch(0.269 0 0);
	--sidebar-accent-foreground: var(--onspry-white);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: var(--onspry-orange);
}

@utility logo {
	display: block;
	box-sizing: border-box;
	background: var(--logo);
	background-size: contain;
	width: 180px;
	height: 32px;
	background-position: center center;
	background-repeat: no-repeat;

	@media (max-width: 640px) {
		width: 120px;
		height: 24px;
	}
}

@theme inline {
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
}

/* ONSPRY Brand Utilities - Single-purpose atomic utilities */
@utility text-brand {
	color: var(--onspry-orange);
}

@utility bg-brand {
	background-color: var(--onspry-orange);
}

@utility border-brand {
	border-color: var(--onspry-orange);
}

@utility shadow-brand {
	box-shadow: 0 4px 20px hsl(var(--onspry-orange-hsl) / 0.15);
}

/* Smart brand utilities that adapt to dark/light mode automatically */
@utility text-adaptive {
	color: var(--foreground);
}

@utility bg-adaptive {
	background-color: var(--background);
}

@utility text-muted-adaptive {
	color: var(--muted-foreground);
}

@utility bg-card-adaptive {
	background-color: var(--card);
}

/* Component classes for common patterns - Multi-property components */
.container-section {
	/* Standard section container with responsive padding */
	@apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
}

.card-base {
	/* Base card styling - borderless by default */
	@apply relative flex flex-col rounded-2xl bg-background/80 shadow-sm transition-all duration-200;
}

.card-hover {
	/* Global hover effects for all interactive cards */
	@apply transition-all duration-300 hover:-translate-y-1;
	border: 1px solid transparent;
}

.card-hover:hover {
	/* Light mode: orange border + glow */
	border-color: hsl(var(--onspry-orange-hsl) / 0.6);
	box-shadow: 0 4px 12px hsl(var(--onspry-orange-hsl) / 0.2);
}

.dark .card-hover:hover {
	/* Dark mode: enhanced orange glow with border */
	border-color: hsl(var(--onspry-orange-hsl) / 0.8);
	box-shadow:
		0 4px 12px hsl(var(--onspry-orange-hsl) / 0.3),
		0 0 0 1px hsl(var(--onspry-orange-hsl) / 0.4);
}

.variant-selected {
	/* Selected variant styling - persistent orange border */
	border-color: hsl(var(--onspry-orange-hsl) / 0.6) !important;
	box-shadow: 0 4px 12px hsl(var(--onspry-orange-hsl) / 0.2) !important;
}

.dark .variant-selected {
	/* Dark mode: selected variant with enhanced orange glow */
	border-color: hsl(var(--onspry-orange-hsl) / 0.8) !important;
	box-shadow:
		0 4px 12px hsl(var(--onspry-orange-hsl) / 0.3),
		0 0 0 1px hsl(var(--onspry-orange-hsl) / 0.4) !important;
}

.card-feature {
	/* Feature card specific styling - borderless with hover border */
	@apply flex flex-col items-center rounded-lg bg-background/80 p-5 shadow backdrop-blur-sm transition-all hover:-translate-y-1 hover:border hover:border-orange-200 hover:shadow-lg dark:bg-background/80 dark:hover:border-orange-900;
}

.icon-container {
	/* Icon container with ONSPRY orange styling */
	@apply mb-3 flex h-10 w-10 items-center justify-center rounded-full bg-orange-100 text-primary transition-colors dark:bg-orange-900;
}

.image-container {
	/* Product image container with gradient background */
	@apply relative flex w-full items-center justify-center overflow-hidden bg-gradient-to-br from-muted/40 to-background/80;
}

.badge-new {
	/* New product badge */
	@apply absolute top-3 left-3 rounded bg-primary px-2 py-1 text-xs font-bold text-white shadow;
}

.badge-stock {
	/* Low stock badge */
	@apply absolute top-3 right-3 rounded bg-red-500 px-2 py-1 text-xs font-bold text-white shadow;
}

.section-header {
	/* Section header with consistent spacing */
	@apply mb-8 text-center;
}


/* Enhanced hover effects that work beautifully in both light and dark modes */
.hover-lift {
	/* Subtle lift with adaptive shadow */
	@apply transition-all duration-200 hover:-translate-y-1;
	/* Light mode: traditional shadow */
	box-shadow: 0 1px 3px hsl(var(--foreground) / 0.1);
}

.hover-lift:hover {
	/* Light mode: enhanced shadow */
	box-shadow: 0 4px 12px hsl(var(--foreground) / 0.15);
}

.dark .hover-lift {
	/* Dark mode: subtle glow instead of shadow */
	box-shadow: 0 1px 3px hsl(var(--background) / 0.05);
}

.dark .hover-lift:hover {
	/* Dark mode: enhanced glow with orange accent */
	box-shadow:
		0 4px 12px hsl(var(--foreground) / 0.1),
		0 0 0 1px hsl(var(--primary) / 0.2),
		0 0 20px hsl(var(--primary) / 0.1);
}

.hover-glow {
	/* Glow effect that works in both modes */
	@apply transition-all duration-300;
	border: 1px solid transparent;
}

.hover-glow:hover {
	/* Light mode: border + shadow */
	border-color: hsl(var(--onspry-orange-hsl) / 0.4);
	box-shadow: 0 2px 8px hsl(var(--onspry-orange-hsl) / 0.15);
}

.dark .hover-glow:hover {
	/* Dark mode: glowing border + inner glow */
	border-color: hsl(var(--onspry-orange-hsl) / 0.6);
	box-shadow:
		0 0 0 1px hsl(var(--onspry-orange-hsl) / 0.3),
		inset 0 1px 0 hsl(var(--foreground) / 0.1),
		0 2px 12px hsl(var(--onspry-orange-hsl) / 0.2);
}

.hover-shine {
	/* Shine effect for interactive elements */
	@apply relative overflow-hidden transition-all duration-200;
}

.hover-shine::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(
		90deg,
		transparent,
		hsl(var(--foreground) / 0.1),
		transparent
	);
	transition: left 0.5s ease;
}

.hover-shine:hover::before {
	left: 100%;
}

.dark .hover-shine::before {
	background: linear-gradient(
		90deg,
		transparent,
		hsl(var(--primary) / 0.2),
		transparent
	);
}

/* Centralized Button Enhancement System */



/* Arrow button animations - right direction (forward actions) */
.btn-arrow-right .arrow-icon {
	transition: transform 0.3s ease;
}

.btn-arrow-right:hover .arrow-icon {
	transform: translateX(0.25rem);
}

/* Arrow button animations - left direction (back actions) */
.btn-arrow-left .arrow-icon {
	transition: transform 0.3s ease;
}

.btn-arrow-left:hover .arrow-icon {
	transform: translateX(-0.25rem);
}

/* Enhanced button hover effects */
.btn-enhanced {
	transition: all 0.2s ease;
}

.btn-enhanced:hover {
	/* Light mode: subtle glow */
	box-shadow: 0 2px 8px hsl(var(--onspry-orange-hsl) / 0.25);
}

.dark .btn-enhanced:hover {
	/* Dark mode: enhanced glow */
	box-shadow:
		0 2px 12px hsl(var(--onspry-orange-hsl) / 0.3),
		0 0 0 1px hsl(var(--onspry-orange-hsl) / 0.4);
}

/* Shimmer effect for special buttons (optional) */
.btn-shimmer {
	position: relative;
	overflow: hidden;
}

.btn-shimmer::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(
		90deg,
		transparent,
		hsl(var(--foreground) / 0.1),
		transparent
	);
	transition: left 0.5s ease;
}

.btn-shimmer:hover::before {
	left: 100%;
}

.dark .btn-shimmer::before {
	background: linear-gradient(
		90deg,
		transparent,
		hsl(var(--onspry-orange-hsl) / 0.2),
		transparent
	);
}

.section-subtitle {
	/* Section subtitle styling */
	@apply text-muted-foreground mx-auto mt-3 max-w-xl;
}

/* Font loading states */
.fonts-loading * {
	transition: none !important;
}

/* When fonts are loaded, ensure everything uses the correct fonts */
html.fonts-loaded body {
	font-family: 'Inter', sans-serif;
}

html.fonts-loaded h1,
html.fonts-loaded h2,
html.fonts-loaded h3,
html.fonts-loaded h4,
html.fonts-loaded h5,
html.fonts-loaded h6 {
	font-family: 'Onest', sans-serif;
}

/* Global heading styles - consistent typography across the site */
h1 {
	@apply text-2xl tracking-tight lg:text-3xl;
}

h2 {
	@apply text-xl tracking-tight lg:text-2xl;
}

h3 {
	@apply text-lg tracking-tight lg:text-xl;
}

h4 {
	@apply text-base tracking-tight lg:text-lg;
}

h5 {
	@apply text-sm tracking-tight lg:text-base;
}

h6 {
	@apply text-xs tracking-tight lg:text-sm;
}

/* Explicitly set fonts for specific component types */
html.fonts-loaded [data-accordion-content],
html.fonts-loaded [data-accordion-trigger],
html.fonts-loaded [role='combobox'],
html.fonts-loaded select,
html.fonts-loaded input,
html.fonts-loaded button {
	font-family: 'Inter', sans-serif;
}

/* Backup rules for when fonts fail to load */
html.fonts-failed body {
	font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Toast Styles - ONSPRY branded using classes */

/* Success toasts */
.toast-success {
	background-color: #FF6B00 !important; /* ONSPRY Orange */
	color: white !important;
	border: 2px solid #FF6B00 !important;
	border-radius: 12px !important;
	box-shadow: 0 4px 16px rgba(255, 107, 0, 0.3) !important;
}

.toast-success * {
	color: white !important;
}

/* Error toasts */
.toast-error {
	background-color: hsl(var(--destructive)) !important;
	color: white !important;
	border: 2px solid hsl(var(--destructive)) !important;
	border-radius: 12px !important;
	box-shadow: 0 4px 16px hsl(var(--destructive) / 0.3) !important;
}

.toast-error * {
	color: white !important;
}

/* Action buttons */
.toast-action {
	background: white !important;
	color: #FF6B00 !important;
	border: 1px solid white !important;
	border-radius: 6px !important;
	padding: 8px 12px !important;
	font-weight: 500 !important;
	margin-left: 8px !important;
}

.toast-action:hover {
	background: #f5f5f5 !important;
	color: #FF6B00 !important;
}

/* Error toast action buttons */
.toast-error .toast-action {
	color: hsl(var(--destructive)) !important;
}

.toast-error .toast-action:hover {
	color: hsl(var(--destructive)) !important;
}

/* Close button styling */
.toast-success [data-close-button],
.toast-error [data-close-button] {
	background: rgba(255, 255, 255, 0.2) !important;
	color: white !important;
	border: 1px solid rgba(255, 255, 255, 0.3) !important;
	border-radius: 50% !important;
	width: 24px !important;
	height: 24px !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	cursor: pointer !important;
	opacity: 1 !important;
	pointer-events: auto !important;
}

.toast-success [data-close-button]:hover,
.toast-error [data-close-button]:hover {
	background: rgba(255, 255, 255, 0.3) !important;
}



@layer base {
	body {
		@apply bg-background text-foreground;
	}

	/* Restore border colors for all border utilities */
	.border,
	.border-t,
	.border-b,
	.border-l,
	.border-r,
	.border-x,
	.border-y {
		border-color: var(--border);
	}

	/* Ensure hover border colors work properly */
	.hover\:border:hover,
	.hover\:border-primary:hover {
		border-color: hsl(var(--primary));
	}
}
