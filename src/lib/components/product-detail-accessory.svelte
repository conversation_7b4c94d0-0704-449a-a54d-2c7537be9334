<script lang="ts">
	import * as m from '$lib/paraglide/messages.js';
	import type {
		ProductViewModel,
		ProductVariantViewModel,
		ProductImageViewModel
	} from '$lib/models/product';
	import type { CartViewModel } from '$lib/models/cart';
	import { Button } from '$lib/components/ui/button';

	import VariantCard from '$lib/components/variant-card.svelte';
	// AppImage is now used via ProductImageGallery
	import ProductImageGallery from '$lib/components/product-image-gallery.svelte';
	import { formatPrice } from '$lib/utils/price';
	import { getPriceForLocale } from '$lib/utils/price';
	import { ShoppingCart, Check, Minus, Plus } from 'lucide-svelte';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import { cart } from '$lib/stores/cart';

	import { localizeHref, getLocale } from '$lib/paraglide/runtime';

	let { product, variants, images } = $props<{
		product: ProductViewModel;
		variants: ProductVariantViewModel[];
		images: ProductImageViewModel[];
	}>();

	// Derived data
	const basePrice = $derived.by(() => {
		if (!variants?.length) return 0;
		const locale = getLocale();
		const prices = variants.map((v: ProductVariantViewModel) =>
			getPriceForLocale(v.prices, locale, 0)
		);
		return Math.min(...prices);
	});

	// State management for selected options
	let selectedVariantId = $state<string | null>(null);

	// Set default selected variant
	$effect(() => {
		if (variants?.length > 0 && !selectedVariantId) {
			selectedVariantId = variants[0].id;
		}
	});

	// Get the selected variant based on current selection
	const selectedVariant = $derived(
		variants?.find((v: ProductVariantViewModel) => v.id === selectedVariantId) || variants?.[0]
	);

	// Handle variant selection
	function selectVariant(variantId: string) {
		selectedVariantId = variantId;
	}

	// Quantity counter
	let quantity = $state(1);

	// Add to cart state
	let isAddingToCart = $state(false);
	let addedToCart = $state(false);

	// Helper function to get specific variant attributes
	function getVariantAttribute(variant: ProductVariantViewModel, attribute: string): string {
		return (variant.attributes?.[attribute] as string) || '';
	}

	// Add to cart functionality
	function handleAddToCartResult(result: {
		type: string;
		status?: number;
		data?: { cart?: CartViewModel; message?: string };
	}) {
		if (result.type === 'success') {
			addedToCart = true;

			// Update the cart store with the returned data
			if (result.data?.cart) {
				console.log('Setting cart with data from product page:', result.data.cart);
				cart.set(result.data.cart);
			}

			// Show success toast with checkout and view cart actions
			toast.success(`${product.name} added to your cart!`, {
				action: {
					label: 'View Cart',
					onClick: () => goto(localizeHref('/cart'))
				},
				duration: 3000 // Shorter duration for success toast
			});

			// Reset the success message after 3 seconds, but keep the selection
			setTimeout(() => {
				addedToCart = false;
			}, 3000);
		} else {
			// Handle error cases
			const errorMessage = result.data?.message || m.cart_add_error();
			toast.error(errorMessage);
		}

		// Reset loading state
		isAddingToCart = false;
	}

	// Check if variant is available
	const isVariantAvailable = $derived(
		!!selectedVariant && selectedVariant.stockStatus !== 'out_of_stock'
	);

	// Add current locale derivation
	const currentLocale = $derived(getLocale());

	// Image handling is now done by ProductImageGallery component
</script>

<div>
	{#if !product?.id}
		<div class="flex h-64 items-center justify-center">
			<div class="border-primary h-12 w-12 animate-spin rounded-full border-t-2 border-b-2"></div>
		</div>
	{:else}
		<div class="w-full py-8">
			<!-- Main Layout: 60/40 split -->
			<div class="w-full space-y-8">
				<!-- Image Gallery and Product Details Side by Side -->
				<div class="grid grid-cols-1 gap-8 md:grid-cols-5">
					<!-- Left: Image Gallery (60% width = 3/5) -->
					<div class="md:col-span-3">
						<ProductImageGallery {images} />
					</div>

					<!-- Right: Complete Product Info Panel (40% width = 2/5) -->
					<div class="space-y-6 md:col-span-2">
						<!-- Product Info Header -->
						<div class="space-y-4">
							<h1 class="text-3xl font-bold tracking-tight">{product.name}</h1>
							<p class="text-muted-foreground text-2xl font-medium">
								{formatPrice(
									selectedVariant
										? getPriceForLocale(selectedVariant.prices, currentLocale, 0)
										: basePrice,
									currentLocale
								)}
							</p>
							{#if product.description}
								<p class="text-muted-foreground text-base leading-relaxed">{product.description}</p>
							{/if}
						</div>

						<!-- Features and Specifications Side by Side -->
						<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
							<!-- Features -->
							{#if product.features?.length > 0}
								<div class="rounded-lg border p-4">
									<h3 class="mb-3 text-lg font-semibold">{m.product_features()}</h3>
									<ul class="list-disc space-y-1 pl-4 text-sm">
										{#each product.features as feature}
											<li class="text-foreground">{feature}</li>
										{/each}
									</ul>
								</div>
							{/if}

							<!-- Specifications -->
							{#if product.specifications && Object.keys(product.specifications).length > 0}
								<div class="rounded-lg border p-4">
									<h3 class="mb-3 text-lg font-semibold">{m.product_specifications()}</h3>
									<div class="space-y-2 text-xs">
										{#each Object.entries(product.specifications) as [key, value]}
											<div class="flex justify-between">
												<span class="text-muted-foreground font-medium capitalize">
													{key.replace(/_/g, ' ')}:
												</span>
												<span class="text-foreground">{String(value)}</span>
											</div>
										{/each}
									</div>
								</div>
							{/if}
						</div>
					</div>
				</div>

				<!-- Accessory Configuration Options (Full width below) -->
				<div class="space-y-6">
					<h2 class="text-2xl font-bold">{product.category} {m.options()}</h2>
					<div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
						{#each variants || [] as variant}
							<VariantCard
								{variant}
								isSelected={selectedVariantId === variant.id}
								onClick={() => selectVariant(variant.id)}
								showPrice={true}
								disabled={isAddingToCart}
							/>
						{/each}
					</div>
				</div>

				<!-- Selected Option Summary -->
				{#if selectedVariant}
					<div class="rounded-lg border p-6">
						<h2 class="mb-4 text-lg font-semibold">{m.product_selected_option()}</h2>
						<div class="space-y-2 text-sm">
							{#if product.category === 'SWITCH'}
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.switch_type()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedVariant, 'type') || 'N/A'}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.actuation_force()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedVariant, 'actuation_force') || 'N/A'}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.feel()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedVariant, 'feel') || 'N/A'}
									</span>
								</div>
							{:else if product.category === 'KEYCAP'}
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.legend_type()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedVariant, 'legend_type') || 'N/A'}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.material()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedVariant, 'material') || 'N/A'}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.color()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedVariant, 'color') || 'N/A'}
									</span>
								</div>
							{/if}
						</div>
					</div>
				{/if}
			</div>

			<!-- Bottom Action Bar: Quantity and Add to Cart (Full width, sticky bottom) -->
			<div
				class="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky bottom-0 z-10 backdrop-blur"
			>
				<div class="container mx-auto max-w-7xl px-4 py-4">
					<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
						<!-- Total Price -->
						<div class="flex items-center gap-4">
							<span class="text-base font-medium">{m.product_total()}:</span>
							<span class="text-2xl font-bold">
								{selectedVariant
									? formatPrice(
											getPriceForLocale(selectedVariant.prices, currentLocale, 0) * quantity,
											currentLocale
										)
									: '--'}
							</span>
						</div>

						<!-- Quantity and Add to Cart -->
						<div class="flex items-center gap-4">
							<!-- Quantity selector -->
							<div class="flex items-center gap-2">
								<span class="text-sm font-medium">{m.product_quantity()}:</span>
								<div class="bg-muted/10 flex items-center space-x-1 rounded-md p-1">
									<Button
										variant="ghost"
										size="icon"
										class="text-muted-foreground hover:bg-muted hover:text-foreground h-8 w-8 transition-colors disabled:opacity-50"
										onclick={() => quantity > 1 && (quantity -= 1)}
										disabled={quantity <= 1 || isAddingToCart}
										aria-label={m.decrease_quantity()}><Minus class="h-4 w-4" /></Button
									>
									<span class="w-8 text-center text-sm font-medium tabular-nums">{quantity}</span>
									<Button
										variant="ghost"
										size="icon"
										class="text-muted-foreground hover:bg-muted hover:text-foreground h-8 w-8 transition-colors disabled:opacity-50"
										onclick={() => (quantity += 1)}
										disabled={isAddingToCart}
										aria-label={m.increase_quantity()}><Plus class="h-4 w-4" /></Button
									>
								</div>
							</div>

							<!-- Add to Cart Button -->
							<form
								method="POST"
								action="?/addToCart"
								use:enhance={() => {
									isAddingToCart = true;
									const toastId = toast.loading(`Adding ${product.name} to cart...`, {
										duration: 30000
									});
									return async ({ result }) => {
										toast.dismiss(toastId);
										handleAddToCartResult(result);
									};
								}}
							>
								<input type="hidden" name="productVariantId" value={selectedVariant?.id || ''} />
								<input type="hidden" name="quantity" value={quantity} />
								<Button
									type="submit"
									size="lg"
									class="btn-enhanced min-w-[180px]"
									disabled={!isVariantAvailable || isAddingToCart}
								>
									{#if isAddingToCart}
										<span
											class="border-primary-foreground mr-2 block h-5 w-5 animate-spin rounded-full border-2 border-t-transparent"
										></span>
										<span>{m.adding()}</span>
									{:else if addedToCart}
										<Check class="mr-2 h-5 w-5" />
										<span>{m.added_to_cart()}</span>
									{:else if selectedVariant?.stockStatus === 'out_of_stock'}
										<span>{m.product_out_of_stock()}</span>
									{:else}
										<ShoppingCart class="mr-2 h-5 w-5" />
										<span>{m.addToCart()}</span>
									{/if}
								</Button>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>
