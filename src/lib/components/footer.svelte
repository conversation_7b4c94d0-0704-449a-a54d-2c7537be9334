<script lang="ts">
	import * as m from '$lib/paraglide/messages.js';
	import { localizeHref } from '$lib/paraglide/runtime';
	// Compute the current year for the copyright text.
	const currentYear = new Date().getFullYear();
</script>

<div class="flex h-full w-full items-center justify-between py-4">
	<p class="text-muted-foreground text-sm">
		&copy; {currentYear} Onspry. {m.footer_text({ year: currentYear })}
	</p>
	<nav class="text-muted-foreground flex gap-4 text-sm">
		<a href={localizeHref('/privacy')} class="hover:text-foreground hover:underline"
			>{m.footer_privacy()}</a
		>
		<a href={localizeHref('/terms')} class="hover:text-foreground hover:underline"
			>{m.footer_terms()}</a
		>
	</nav>
</div>
