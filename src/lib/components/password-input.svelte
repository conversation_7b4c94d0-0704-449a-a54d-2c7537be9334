<script lang="ts">
	import { Eye, EyeOff } from 'lucide-svelte/icons';
	import { Button } from './ui/button';
	import * as m from '$lib/paraglide/messages';

	// Using $props() for Svelte 5 with oninput callback
	let { value, id, name, placeholder, autocomplete, ariaInvalid, disabled, oninput } = $props<{
		value: string;
		id: string;
		name: string;
		placeholder?: string;
		autocomplete?: string;
		ariaInvalid?: string;
		disabled?: boolean;
		oninput?: (value: string) => void;
	}>();

	// Handle input changes
	function handleInput(e: Event) {
		const target = e.target as HTMLInputElement;
		if (oninput) oninput(target.value);
	}

	// Toggle password visibility
	let showPassword = $state(false);
	const toggleVisibility = () => {
		showPassword = !showPassword;
	};
</script>

<div class="relative">
	<input
		{id}
		{name}
		type={showPassword ? 'text' : 'password'}
		{value}
		oninput={handleInput}
		{placeholder}
		{autocomplete}
		aria-invalid={ariaInvalid}
		{disabled}
		class="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 pr-10 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
	/>
	<Button
		type="button"
		variant="ghost"
		size="sm"
		class="text-muted-foreground hover:text-foreground absolute top-1/2 right-1 h-8 w-8 -translate-y-1/2 p-0 hover:bg-transparent"
		onclick={toggleVisibility}
		tabindex={-1}
		aria-label={showPassword ? m.aria_hide_password() : m.aria_show_password()}
		{disabled}
	>
		{#if showPassword}
			<EyeOff class="h-4 w-4" />
		{:else}
			<Eye class="h-4 w-4" />
		{/if}
	</Button>
</div>
