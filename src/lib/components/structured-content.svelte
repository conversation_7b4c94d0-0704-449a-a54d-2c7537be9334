<script lang="ts">
	import { onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import * as m from '$lib/paraglide/messages';
	import type { ContentSection, ContentElement } from '$lib/utils/content-parser';

	// Content data props with runes
	const {
		title = '',
		intro = [],
		sections = [],
		showBackButton = true
	} = $props<{
		title?: string;
		intro?: ContentElement[];
		sections?: ContentSection[];
		showBackButton?: boolean;
	}>();

	// Animation state
	let contentVisible = $state(false);

	// Set timeout to prevent flash of content
	onMount(() => {
		const timer = setTimeout(() => {
			contentVisible = true;
		}, 300);

		return () => clearTimeout(timer);
	});
</script>

<div
	class="transition-opacity duration-500"
	class:opacity-0={!contentVisible}
	class:opacity-100={contentVisible}
>
	<div class="container mx-auto max-w-4xl px-4 py-8">
		<div class="space-y-16">
			<!-- Main Title Section -->
			<div class="space-y-6">
				<h1>{title}</h1>

				<!-- Intro Content -->
				{#if intro.length > 0}
					<div class="prose prose-xl dark:prose-invert text-muted-foreground mx-auto max-w-3xl">
						{#each intro as element}
							{#if element.type === 'paragraph'}
								<p class="my-4 leading-relaxed">
									{#each element.children || [] as child}
										{#if child.type === 'text'}
											{child.content}
										{:else if child.type === 'link'}
											<a
												href={child.href}
												target={child.target}
												rel={child.rel}
												class="text-primary hover:underline"
											>
												{child.content}
											</a>
										{:else if child.type === 'strong'}
											<strong>{child.content}</strong>
										{:else if child.type === 'em'}
											<em>{child.content}</em>
										{:else if child.type === 'code'}
											<code class="bg-muted/50 rounded px-1.5 py-0.5 font-mono text-sm"
												>{child.content}</code
											>
										{/if}
									{/each}
								</p>
							{:else if element.type === 'list'}
								<ul class="my-6 space-y-2">
									{#each element.children || [] as item}
										<li class="leading-relaxed">
											{#each item.children || [] as child}
												{#if child.type === 'text'}
													{child.content}
												{:else if child.type === 'link'}
													<a
														href={child.href}
														target={child.target}
														rel={child.rel}
														class="text-primary hover:underline"
													>
														{child.content}
													</a>
												{:else if child.type === 'strong'}
													<strong>{child.content}</strong>
												{:else if child.type === 'em'}
													<em>{child.content}</em>
												{:else if child.type === 'code'}
													<code class="bg-muted/50 rounded px-1.5 py-0.5 font-mono text-sm"
														>{child.content}</code
													>
												{/if}
											{/each}
										</li>
									{/each}
								</ul>
							{:else if element.type === 'blockquote'}
								<blockquote
									class="border-muted-foreground/20 text-muted-foreground my-6 border-l-2 pl-6 italic"
								>
									{#each element.children || [] as child}
										{#if child.type === 'text'}
											{child.content}
										{:else if child.type === 'link'}
											<a
												href={child.href}
												target={child.target}
												rel={child.rel}
												class="text-primary hover:underline"
											>
												{child.content}
											</a>
										{:else if child.type === 'strong'}
											<strong>{child.content}</strong>
										{:else if child.type === 'em'}
											<em>{child.content}</em>
										{:else if child.type === 'code'}
											<code class="bg-muted/50 rounded px-1.5 py-0.5 font-mono text-sm"
												>{child.content}</code
											>
										{/if}
									{/each}
								</blockquote>
							{/if}
						{/each}
					</div>
				{/if}
			</div>

			<!-- Content Sections -->
			{#each sections as section}
				<section class="space-y-8">
					<div>
						<h2>{section.title}</h2>
					</div>
					<div class="prose prose-lg dark:prose-invert mx-auto max-w-3xl">
						{#each section.content as element}
							{#if element.type === 'paragraph'}
								<p class="my-6 text-lg leading-relaxed">
									{#each element.children || [] as child}
										{#if child.type === 'text'}
											{child.content}
										{:else if child.type === 'link'}
											<a
												href={child.href}
												target={child.target}
												rel={child.rel}
												class="text-primary hover:underline"
											>
												{child.content}
											</a>
										{:else if child.type === 'strong'}
											<strong>{child.content}</strong>
										{:else if child.type === 'em'}
											<em>{child.content}</em>
										{:else if child.type === 'code'}
											<code class="bg-muted/50 rounded px-1.5 py-0.5 font-mono text-sm"
												>{child.content}</code
											>
										{/if}
									{/each}
								</p>
							{:else if element.type === 'list'}
								<ul class="my-6 space-y-2">
									{#each element.children || [] as item}
										<li class="leading-relaxed">
											{#each item.children || [] as child}
												{#if child.type === 'text'}
													{child.content}
												{:else if child.type === 'link'}
													<a
														href={child.href}
														target={child.target}
														rel={child.rel}
														class="text-primary hover:underline"
													>
														{child.content}
													</a>
												{:else if child.type === 'strong'}
													<strong>{child.content}</strong>
												{:else if child.type === 'em'}
													<em>{child.content}</em>
												{:else if child.type === 'code'}
													<code class="bg-muted/50 rounded px-1.5 py-0.5 font-mono text-sm"
														>{child.content}</code
													>
												{/if}
											{/each}
										</li>
									{/each}
								</ul>
							{:else if element.type === 'blockquote'}
								<blockquote
									class="border-muted-foreground/20 text-muted-foreground my-6 border-l-2 pl-6 italic"
								>
									{#each element.children || [] as child}
										{#if child.type === 'text'}
											{child.content}
										{:else if child.type === 'link'}
											<a
												href={child.href}
												target={child.target}
												rel={child.rel}
												class="text-primary hover:underline"
											>
												{child.content}
											</a>
										{:else if child.type === 'strong'}
											<strong>{child.content}</strong>
										{:else if child.type === 'em'}
											<em>{child.content}</em>
										{:else if child.type === 'code'}
											<code class="bg-muted/50 rounded px-1.5 py-0.5 font-mono text-sm"
												>{child.content}</code
											>
										{/if}
									{/each}
								</blockquote>
							{/if}
						{/each}
					</div>
					{#if section.children.length > 0}
						{#each section.children as child}
							<div class="mt-12 space-y-6">
								<div>
									<h3>{child.title}</h3>
								</div>
								<div class="prose prose-lg dark:prose-invert mx-auto max-w-3xl">
									{#each child.content as element}
										{#if element.type === 'paragraph'}
											<p class="my-4 leading-relaxed">
												{#each element.children || [] as grandchild}
													{#if grandchild.type === 'text'}
														{grandchild.content}
													{:else if grandchild.type === 'link'}
														<a
															href={grandchild.href}
															target={grandchild.target}
															rel={grandchild.rel}
															class="text-primary hover:underline"
														>
															{grandchild.content}
														</a>
													{:else if grandchild.type === 'strong'}
														<strong>{grandchild.content}</strong>
													{:else if grandchild.type === 'em'}
														<em>{grandchild.content}</em>
													{:else if grandchild.type === 'code'}
														<code class="bg-muted/50 rounded px-1.5 py-0.5 font-mono text-sm"
															>{grandchild.content}</code
														>
													{/if}
												{/each}
											</p>
										{:else if element.type === 'list'}
											<ul class="my-6 space-y-2">
												{#each element.children || [] as item}
													<li class="leading-relaxed">
														{#each item.children || [] as grandchild}
															{#if grandchild.type === 'text'}
																{grandchild.content}
															{:else if grandchild.type === 'link'}
																<a
																	href={grandchild.href}
																	target={grandchild.target}
																	rel={grandchild.rel}
																	class="text-primary hover:underline"
																>
																	{grandchild.content}
																</a>
															{:else if grandchild.type === 'strong'}
																<strong>{grandchild.content}</strong>
															{:else if grandchild.type === 'em'}
																<em>{grandchild.content}</em>
															{:else if grandchild.type === 'code'}
																<code class="bg-muted/50 rounded px-1.5 py-0.5 font-mono text-sm"
																	>{grandchild.content}</code
																>
															{/if}
														{/each}
													</li>
												{/each}
											</ul>
										{/if}
									{/each}
								</div>
							</div>
						{/each}
					{/if}
				</section>
			{/each}

			<!-- Back Button -->
			{#if showBackButton}
				<div class="flex justify-center pt-8">
					<Button variant="outline" onclick={() => window.history.back()}>{m.button_back()}</Button>
				</div>
			{/if}
		</div>
	</div>
</div>
