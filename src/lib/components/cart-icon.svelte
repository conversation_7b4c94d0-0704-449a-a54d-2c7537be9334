<script lang="ts">
	import { ShoppingCart } from 'lucide-svelte/icons';
	import { cart } from '$lib/stores/cart';
	import { m } from '$lib/paraglide/messages.js';
	import { localizeHref } from '$lib/paraglide/runtime';

	// Simple state, with no derived reactivity
	let currentCount = $state(0);
	let shouldAnimate = $state(false);
	let initialized = $state(false);

	// Update the count when the cart store changes
	$effect(() => {
		const newCount = $cart.itemCount || 0;
		const oldCount = currentCount;

		// Update the count
		currentCount = newCount;

		// Don't animate on first load
		if (!initialized) {
			initialized = true;
			return;
		}

		// Animate only when count increases
		if (newCount > oldCount) {
			shouldAnimate = true;
			// Reset animation after 1 second
			setTimeout(() => {
				shouldAnimate = false;
			}, 1000);
		}
	});
</script>

<a href={localizeHref('/cart')} class="relative" aria-label={m.nav_cart()} data-sveltekit-reload>
	<ShoppingCart strokeWidth={1.5} size={24} />

	{#if currentCount > 0}
		<span
			class="bg-primary absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center rounded-full text-xs font-bold text-white"
			class:animate-bounce={shouldAnimate}
		>
			{currentCount}
		</span>
	{/if}
</a>
