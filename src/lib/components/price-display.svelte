<script lang="ts">
	import { formatPrice } from '$lib/utils/price';
	import { getLocale } from '$lib/paraglide/runtime.js';
	import type { Locale } from '$lib/utils/localization';

	// Props
	const { price } = $props<{
		price: number;
	}>();

	// Use derived state - Safari compatible
	const formattedPrice = $derived.by(() => {
		const locale = getLocale() as Locale;
		return formatPrice(price, locale);
	});
</script>

<span class="price">{formattedPrice}</span>

<style>
	.price {
		font-weight: 600;
	}
</style>
