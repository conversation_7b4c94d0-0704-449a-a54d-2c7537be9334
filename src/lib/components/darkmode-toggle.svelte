<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import Sun from 'lucide-svelte/icons/sun';
	import Moon from 'lucide-svelte/icons/moon';
	import { theme } from '$lib/stores/theme';
	import * as m from '$lib/paraglide/messages';

	// Subscribe to theme changes and update the DOM
	$effect(() => {
		const isDark = $theme === 'dark';
		document.documentElement.classList.toggle('dark', isDark);
	});
</script>

<Button onclick={() => theme.toggle()} variant="ghost" size="icon">
	<Sun
		class="h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90"
	/>
	<Moon
		class="absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0"
	/>
	<span class="sr-only">{m.sr_toggle_theme()}</span>
</Button>
