<script lang="ts">
	import { DropdownMenu as DropdownMenuPrimitive } from 'bits-ui';
	import { cn } from '$lib/utils.js';

	let className: string | undefined | null = undefined;
	export { className as class };
</script>

<DropdownMenuPrimitive.Trigger
	class={cn(
		'ring-offset-background focus-visible:ring-ring data-[state=open]:bg-muted inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50',
		className
	)}
	{...$$restProps}
>
	<slot />
</DropdownMenuPrimitive.Trigger>
