<script lang="ts">
	import { ImageOff } from 'lucide-svelte';
	import { cn } from '$lib/utils';

	const props = $props<{
		src: string;
		alt?: string;
		width?: number | string;
		height?: number | string;
		className?: string;
		aspectRatio?: string;
		objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
		showPlaceholder?: boolean;
		thumbnailMode?: boolean;
		isSelected?: boolean;
	}>();

	// Default values
	const objectFit = props.objectFit || 'contain';
	const showPlaceholder = props.showPlaceholder !== false;
	const thumbnailMode = props.thumbnailMode || false;

	// Derive isSelected from props
	const isSelected = $derived(props.isSelected || false);

	// State
	let isLoading = $state(true);
	let hasError = $state(false);
	let retryCount = $state(0);
	const maxRetries = 2;

	// Function to get optimized image URL
	function getOptimizedImageUrl(url: string): string {
		if (!url) {
			return '';
		}

		// If it's already a full URL, return it as is
		if (url.startsWith('http')) {
			return url;
		}

		// For Netlify deployment, serve images directly from static folder
		// Remove the /images/ prefix handling that was causing issues
		if (url.startsWith('/images/')) {
			// Convert /images/path to direct static path
			return url;
		}

		// If it's a relative path without the /images/ prefix
		if (!url.startsWith('/')) {
			return `/images/${url}`;
		}

		// Default case - just return the URL
		return url;
	}

	// Reset error state when src changes
	$effect(() => {
		if (props.src) {
			isLoading = true;
			hasError = false;
			retryCount = 0;
		}
	});

	// Handle image load
	function handleLoad() {
		isLoading = false;
		retryCount = 0;
	}

	// Handle image error with retry logic
	function handleError(event: Event) {
		const img = event.target as HTMLImageElement;

		if (retryCount < maxRetries) {
			// Retry loading the image after a short delay
			retryCount++;
			setTimeout(() => {
				if (img) {
					img.src = getOptimizedImageUrl(props.src);
				}
			}, 1000 * retryCount); // Increasing delay: 1s, 2s
		} else {
			hasError = true;
			isLoading = false;
		}
	}
</script>

<div
	class={cn(
		'bg-muted/5 relative flex items-center justify-center overflow-hidden transition-all duration-200',
		thumbnailMode ? 'aspect-square rounded-md' : '',
		thumbnailMode && !isSelected ? 'border-border/50 border' : '',
		thumbnailMode && isSelected ? 'border-primary border' : '',
		props.className
	)}
	style:width={typeof props.width === 'number' ? `${props.width}px` : props.width}
	style:height={typeof props.height === 'number' ? `${props.height}px` : props.height}
	style:aspect-ratio={props.aspectRatio || 'auto'}
>
	{#if hasError}
		<!-- Error state -->
		<div class="bg-muted/30 absolute inset-0 flex items-center justify-center backdrop-blur-sm">
			<div class="text-muted-foreground bg-background/80 rounded-full p-2">
				<ImageOff size={thumbnailMode ? 24 : 48} strokeWidth={1.5} />
			</div>
		</div>
	{:else}
		<!-- Image -->
		<img
			src={getOptimizedImageUrl(props.src)}
			alt={props.alt ?? ''}
			width={typeof props.width === 'number' ? props.width : undefined}
			height={typeof props.height === 'number' ? props.height : undefined}
			loading={thumbnailMode || props.src.includes('hero') || props.src.includes('main')
				? 'eager'
				: 'lazy'}
			class={cn(
				'relative z-0 transition-opacity duration-300',
				`object-${objectFit}`,
				isLoading ? 'opacity-0' : 'opacity-100',
				'mx-auto h-auto max-h-full w-auto max-w-full'
			)}
			style="display: block; margin: 0 auto;"
			onload={handleLoad}
			onerror={handleError}
		/>

		<!-- Loading state -->
		{#if isLoading && showPlaceholder}
			<div class="absolute inset-0 z-5">
				<div class="bg-muted-foreground/10 absolute inset-0 animate-pulse rounded-md"></div>
			</div>
		{/if}
	{/if}
</div>

<style>
	img {
		/* Hide the default browser broken image icon */
		&:-moz-broken {
			-moz-force-broken-image-icon: 0;
		}
	}
</style>
