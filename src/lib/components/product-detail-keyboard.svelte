<script lang="ts">
	import * as m from '$lib/paraglide/messages.js';
	import type {
		ProductViewModel,
		ProductVariantViewModel,
		ProductImageViewModel
	} from '$lib/models/product';
	import type { CartViewModel } from '$lib/models/cart';
	import { Button } from '$lib/components/ui/button';

	import VariantCard from '$lib/components/variant-card.svelte';
	// AppImage is now used via ProductImageGallery
	import ProductImageGallery from '$lib/components/product-image-gallery.svelte';
	import { formatPrice } from '$lib/utils/price';
	import { getPriceForLocale } from '$lib/utils/price';
	import { ProductCompatibilityService } from '$lib/utils/product-compatibility';
	import { browser } from '$app/environment';
	import { goto, pushState } from '$app/navigation';
	import { ShoppingCart, Check, Minus, Plus } from 'lucide-svelte';
	import { toast } from 'svelte-sonner';
	import { enhance } from '$app/forms';
	import { cart } from '$lib/stores/cart';
	import { localizeHref, getLocale } from '$lib/paraglide/runtime';

	// Props
	let {
		product,
		variants,
		images,
		switches = [],
		keycaps = [],
		onvariantselect = () => {},
		onSwitchSelect = () => {},
		onKeycapSelect = () => {},
		searchParams
	} = $props<{
		product: ProductViewModel;
		variants: ProductVariantViewModel[];
		images: ProductImageViewModel[];
		switches?: ProductViewModel[];
		keycaps?: ProductViewModel[];
		onvariantselect?: (e: CustomEvent<{ variantId: string }>) => void;
		onSwitchSelect?: (switchId: string | null) => void;
		onKeycapSelect?: (keycapId: string | null) => void;
		searchParams: { variant: string | null; switch: string | null; keycap: string | null };
	}>();

	// Core state variables
	let currentVariantId = $state<string | null>(null);
	let currentSwitchId = $state<string | null>(null);
	let currentKeycapId = $state<string | null>(null);
	let isLoading = $state(false);

	// Selection state is tracked through the individual variables above

	// Derived states for selections and compatibility
	let selectedVariant = $state<ProductVariantViewModel | null>(null);
	let compatibleSwitches = $state<ProductVariantViewModel[]>([]);
	let selectedSwitch = $state<ProductVariantViewModel | null>(null);
	let compatibleKeycaps = $state<ProductVariantViewModel[]>([]);
	let selectedKeycap = $state<ProductVariantViewModel | null>(null);
	// Selection state is tracked through the individual variables above

	// Get initial selections from URL parameters
	const urlVariantId = searchParams.variant;
	const urlSwitchId = searchParams.switch;
	const urlKeycapId = searchParams.keycap;

	// Initialize state with URL values if they exist
	currentVariantId = urlVariantId || null;
	currentSwitchId = urlSwitchId || null;
	currentKeycapId = urlKeycapId || null;

	// Update derived values
	$effect(() => {
		selectedVariant = !currentVariantId
			? null
			: (variants.find((v: ProductVariantViewModel) => v.id === currentVariantId) ?? null);
	});

	$effect(() => {
		if (!selectedVariant) {
			compatibleSwitches = [];
			return;
		}
		compatibleSwitches = ProductCompatibilityService.filterCompatibleProducts(
			product,
			selectedVariant,
			switches
		).flatMap((s) => s.variants);
	});

	$effect(() => {
		selectedSwitch = !currentSwitchId
			? null
			: (compatibleSwitches.find((v: ProductVariantViewModel) => v.id === currentSwitchId) ?? null);
	});

	$effect(() => {
		if (!selectedSwitch) {
			compatibleKeycaps = [];
			return;
		}

		// First try standard compatibility filtering through the service
		let filteredKeycaps = ProductCompatibilityService.filterCompatibleProducts(
			product,
			selectedSwitch,
			keycaps
		).flatMap((k) => k.variants);

		// If we have no results, and we have keycaps available, check the stemType attribute directly
		// This is a fallback for multilingual content that might not be properly structured
		if (filteredKeycaps.length === 0 && keycaps.length > 0) {
			const switchStemType = getVariantAttribute(selectedSwitch, 'stemType', '');
			if (switchStemType) {
				// Get all keycap variants that support this stem type
				filteredKeycaps = keycaps
					.flatMap((k: ProductViewModel) => k.variants)
					.filter((keycapVariant: ProductVariantViewModel) => {
						const keycapStemTypes = getVariantAttribute<string | string[]>(
							keycapVariant,
							'stemType',
							[]
						);
						if (Array.isArray(keycapStemTypes)) {
							return keycapStemTypes.includes(switchStemType);
						}
						return keycapStemTypes === switchStemType;
					});
			}
		}

		compatibleKeycaps = filteredKeycaps;
	});

	$effect(() => {
		selectedKeycap = !currentKeycapId
			? null
			: (compatibleKeycaps.find((v: ProductVariantViewModel) => v.id === currentKeycapId) ?? null);
	});

	// Selection state is tracked through the individual variables above

	// Track valid images - now handled by ProductImageGallery
	// let validImages = $state<string[]>([]);

	// Initialize valid images
	// $effect(() => {
	// 	if (images) {
	// 		validImages = images.map((img: { url: string; }) => img.url);
	// 	}
	// });

	// Calculate number of valid images - used by ProductImageGallery
	// const validImageCount = $derived(validImages.length);

	// Derived data
	const basePrice = $derived.by(() => {
		if (!variants?.length) return 0;
		const locale = getLocale();
		const prices = variants.map((v: ProductVariantViewModel) =>
			getPriceForLocale(v.prices, locale, 0)
		);
		return Math.min(...prices);
	});

	// Add browser state management using SvelteKit's pushState
	function updateBrowserState(
		variantId: string | null,
		switchId: string | null,
		keycapId: string | null
	) {
		if (!browser) return;

		// Create a query string from the state
		const params = new URLSearchParams();
		if (variantId) params.set('variant', variantId);
		if (switchId) params.set('switch', switchId);
		if (keycapId) params.set('keycap', keycapId);

		const url = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
		pushState(url, { state: { variantId, switchId, keycapId } });
	}

	// Selection handlers
	async function selectVariant(variantId: string) {
		isLoading = true;
		try {
			currentVariantId = variantId;
			currentSwitchId = null;
			currentKeycapId = null;
			updateBrowserState(variantId, null, null);
			onvariantselect(new CustomEvent('variantselect', { detail: { variantId } }));
		} finally {
			isLoading = false;
		}
	}

	async function selectSwitch(switchId: string) {
		if (!selectedVariant) return;
		isLoading = true;
		try {
			currentSwitchId = switchId;
			currentKeycapId = null;
			updateBrowserState(currentVariantId!, switchId, null);
			onSwitchSelect(switchId);
		} finally {
			isLoading = false;
		}
	}

	async function selectKeycap(keycapId: string) {
		if (!selectedSwitch) return;
		isLoading = true;
		try {
			currentKeycapId = keycapId;
			updateBrowserState(currentVariantId!, currentSwitchId!, keycapId);
			onKeycapSelect(keycapId);
		} finally {
			isLoading = false;
		}
	}

	// Handle browser back/forward
	if (browser) {
		window.onpopstate = (event) => {
			if (event.state) {
				currentVariantId = event.state.variantId;
				currentSwitchId = event.state.switchId;
				currentKeycapId = event.state.keycapId;
			}
		};
	}

	// Add new state for cart operations
	let quantity = $state(1);
	let isAddingToCart = $state(false);
	let addedToCart = $state(false);

	// Cart store is imported at the top of the file

	// Function to handle the result of adding to cart
	function handleAddToCartResult(result: { type: string; status?: number; data?: unknown }) {
		if (result.type === 'success') {
			addedToCart = true;

			// Update the cart store with the returned data
			if (result.data && typeof result.data === 'object' && 'cart' in result.data) {
				console.log('Setting cart with data from product page:', result.data.cart);
				cart.set(result.data.cart as CartViewModel);
			}

			// Show success toast with checkout and view cart actions
			toast.success(`${product.name} added to your cart!`, {
				action: {
					label: 'View Cart',
					onClick: () => goto(localizeHref('/cart'))
				},
				duration: 3000 // Shorter duration for success toast
			});

			// Reset the success message after 3 seconds, but keep the selection
			setTimeout(() => {
				addedToCart = false;
			}, 3000);
		} else {
			// Handle error cases
			const errorMessage =
				result.data && typeof result.data === 'object' && 'message' in result.data
					? (result.data.message as string)
					: 'Could not add item to cart. Please try again.';
			toast.error(errorMessage);
		}

		// Reset loading state
		isAddingToCart = false;
	}

	// Image loading state is tracked but not actively used

	// Helper function to get variant attribute with type safety
	function getVariantAttribute<T>(
		variant: ProductVariantViewModel | null,
		key: string,
		defaultValue: T
	): T {
		if (!variant) return defaultValue;
		try {
			const value = variant.attributes[key];
			return (value as T) ?? defaultValue;
		} catch {
			return defaultValue;
		}
	}

	// Helper functions for compatibility are handled by the ProductCompatibilityService

	// Helper function to get button text based on selection state
	function getButtonText(): string {
		if (!selectedVariant) {
			return m.product_select_keyboard();
		}
		if (selectedVariant.stockStatus === 'out_of_stock') {
			return m.product_out_of_stock();
		}
		if (!selectedSwitch) {
			return m.product_select_switch();
		}
		if (!selectedKeycap) {
			return m.product_select_keycap();
		}
		return m.product_add_to_cart();
	}

	// Derived state for cart button
	let canAddToCart = $derived(
		!!selectedVariant &&
			selectedVariant.stockStatus === 'in_stock' &&
			!!selectedSwitch &&
			!!selectedKeycap
	);

	// Image handling is now done by ProductImageGallery component

	// Add current locale derivation
	const currentLocale = $derived(getLocale());
</script>

{#if !product?.id}
	<div class="flex h-64 items-center justify-center">
		<div class="border-primary h-12 w-12 animate-spin rounded-full border-t-2 border-b-2"></div>
	</div>
{:else}
	<div class="w-full py-8">
		<!-- Main Layout: Single column layout -->
		<div class="w-full space-y-8">
			<!-- Image Gallery and Product Details Side by Side -->
			<div class="grid grid-cols-1 gap-8 md:grid-cols-5">
				<!-- Left: Image Gallery (60% width = 3/5) -->
				<div class="md:col-span-3">
					<ProductImageGallery {images} />
				</div>

				<!-- Right: Complete Product Info Panel (40% width = 2/5) -->
				<div class="space-y-6 md:col-span-2">
					<!-- Product Info Header -->
					<div class="space-y-4">
						<h1 class="text-3xl font-bold tracking-tight">{product.name}</h1>
						<p class="text-muted-foreground text-2xl font-medium">
							{formatPrice(
								selectedVariant
									? getPriceForLocale(selectedVariant.prices, currentLocale, 0)
									: basePrice,
								currentLocale
							)}
						</p>
						{#if product.description}
							<p class="text-muted-foreground text-base leading-relaxed">{product.description}</p>
						{/if}
					</div>

					<!-- Features and Specifications Side by Side -->
					<div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
						<!-- Features -->
						{#if product.features?.length > 0}
							<div class="rounded-lg border p-4">
								<h3 class="mb-3 text-lg font-semibold">{m.product_features()}</h3>
								<ul class="list-disc space-y-1 pl-4 text-sm">
									{#each product.features as feature}
										<li class="text-foreground">{feature}</li>
									{/each}
								</ul>
							</div>
						{/if}

						<!-- Specifications -->
						{#if product.specifications && Object.keys(product.specifications).length > 0}
							<div class="rounded-lg border p-4">
								<h3 class="mb-3 text-lg font-semibold">{m.product_specifications()}</h3>
								<div class="space-y-2 text-xs">
									{#each Object.entries(product.specifications) as [key, value]}
										<div class="flex justify-between">
											<span class="text-muted-foreground font-medium capitalize">
												{key.replace(/_/g, ' ')}:
											</span>
											<span class="text-foreground">{String(value)}</span>
										</div>
									{/each}
								</div>
							</div>
						{/if}
					</div>
				</div>
			</div>

			<!-- Configuration Options Below Image -->
			<div class="space-y-8">
				<!-- Step 1: Keyboard Model Selection -->
				<div class="space-y-4">
					<div class="flex items-center gap-3">
						<div
							class="bg-primary flex h-8 w-8 items-center justify-center rounded-full text-white"
						>
							1
						</div>
						<h2>{m.product_model()}</h2>
					</div>
					<div class="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
						{#each variants || [] as variant}
							<VariantCard
								{variant}
								isSelected={currentVariantId === variant.id}
								onClick={() => selectVariant(variant.id)}
								showPrice={true}
								disabled={isLoading || isAddingToCart}
							/>
						{/each}
					</div>
				</div>

				<!-- Step 2: Switch Selection (Only shown after keyboard selection) -->
				{#if selectedVariant}
					<div class="space-y-4">
						<div class="flex items-center gap-3">
							<div
								class="bg-primary flex h-8 w-8 items-center justify-center rounded-full text-white"
							>
								2
							</div>
							<h2>{m.product_compatible_switches()}</h2>
						</div>
						{#if compatibleSwitches.length > 0}
							<div class="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
								{#each compatibleSwitches as switchVariant}
									<VariantCard
										variant={switchVariant}
										isSelected={currentSwitchId === switchVariant.id}
										onClick={() => selectSwitch(switchVariant.id)}
										showPrice={false}
										disabled={isLoading || isAddingToCart}
									/>
								{/each}
							</div>
						{:else}
							<div class="bg-muted/50 rounded-lg border border-dashed p-6">
								<p class="text-muted-foreground text-center text-sm">
									{m.product_no_compatible_switches()}
								</p>
							</div>
						{/if}
					</div>
				{/if}

				<!-- Step 3: Keycap Selection (Only shown after switch selection) -->
				{#if selectedSwitch}
					<div class="space-y-4">
						<div class="flex items-center gap-3">
							<div
								class="bg-primary flex h-8 w-8 items-center justify-center rounded-full text-white"
							>
								3
							</div>
							<h2>{m.product_compatible_keycaps()}</h2>
						</div>
						{#if compatibleKeycaps.length > 0}
							<div class="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
								{#each compatibleKeycaps as keycapVariant}
									<VariantCard
										variant={keycapVariant}
										isSelected={currentKeycapId === keycapVariant.id}
										onClick={() => selectKeycap(keycapVariant.id)}
										showPrice={false}
										disabled={isLoading || isAddingToCart}
									/>
								{/each}
							</div>
						{:else}
							<div class="bg-muted/50 rounded-lg border border-dashed p-6">
								<p class="text-muted-foreground text-center text-sm">
									{m.product_no_compatible_keycaps()}
								</p>
							</div>
						{/if}
					</div>
				{/if}

				<!-- Configuration Summary (Moved from sidebar) -->
				{#if selectedVariant || selectedSwitch || selectedKeycap}
					<div class="rounded-lg border p-6">
						<h2 class="mb-4">{m.product_configuration_summary()}</h2>
						<div class="space-y-2 text-sm">
							{#if selectedVariant}
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.layout()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedVariant, 'layout', 'N/A')}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.keyboard_variant()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedVariant, 'keyboard_variant', 'N/A')}
									</span>
								</div>
							{/if}
							{#if selectedSwitch}
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.switch_type()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedSwitch, 'type', 'N/A')}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.actuation_force()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedSwitch, 'actuation_force', 'N/A')}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.feel()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedSwitch, 'feel', 'N/A')}
									</span>
								</div>
							{/if}
							{#if selectedKeycap}
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.legend_type()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedKeycap, 'legend_type', 'N/A')}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.material()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedKeycap, 'material', 'N/A')}
									</span>
								</div>
								<div class="flex justify-between">
									<span class="text-muted-foreground font-medium">{m.color()}:</span>
									<span class="text-foreground">
										{getVariantAttribute(selectedKeycap, 'color', 'N/A')}
									</span>
								</div>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		</div>

		<!-- Bottom Action Bar: Quantity and Add to Cart (Full width, sticky bottom) -->
		<div
			class="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky bottom-0 z-10 backdrop-blur"
		>
			<div class="container mx-auto max-w-7xl px-4 py-4">
				<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
					<!-- Total Price -->
					<div class="flex items-center gap-4">
						<span class="text-base font-medium">{m.product_total()}:</span>
						<span class="text-2xl font-bold">
							{#if selectedVariant && selectedSwitch && selectedKeycap}
								{formatPrice(
									getPriceForLocale(selectedVariant.prices, currentLocale, 0) * quantity,
									currentLocale
								)}
							{:else if selectedVariant}
								{formatPrice(
									getPriceForLocale(selectedVariant.prices, currentLocale, 0) * quantity,
									currentLocale
								)}
							{:else}
								--
							{/if}
						</span>
					</div>

					<!-- Quantity and Add to Cart -->
					<div class="flex items-center gap-4">
						<!-- Quantity selector -->
						<div class="flex items-center gap-2">
							<span class="text-sm font-medium">{m.product_quantity()}:</span>
							<div class="bg-muted/10 flex items-center space-x-1 rounded-md p-1">
								<Button
									variant="ghost"
									size="icon"
									class="text-muted-foreground hover:bg-muted hover:text-foreground h-8 w-8 transition-colors disabled:opacity-50"
									onclick={() => quantity > 1 && (quantity -= 1)}
									disabled={quantity <= 1 || isAddingToCart}
									aria-label={m.decrease_quantity()}><Minus class="h-4 w-4" /></Button
								>
								<span class="w-8 text-center text-sm font-medium tabular-nums">{quantity}</span>
								<Button
									variant="ghost"
									size="icon"
									class="text-muted-foreground hover:bg-muted hover:text-foreground h-8 w-8 transition-colors disabled:opacity-50"
									onclick={() => (quantity += 1)}
									disabled={isAddingToCart}
									aria-label={m.increase_quantity()}><Plus class="h-4 w-4" /></Button
								>
							</div>
						</div>

						<!-- Add to Cart Button -->
						<form
							method="POST"
							action="?/addToCart"
							use:enhance={() => {
								isAddingToCart = true;
								return async ({ result }) => {
									handleAddToCartResult(result);
								};
							}}
						>
							<input type="hidden" name="productVariantId" value={selectedVariant?.id || ''} />
							<input type="hidden" name="quantity" value={quantity} />
							{#if selectedSwitch}
								<input type="hidden" name="composites[0][variantId]" value={selectedSwitch.id} />
								<input type="hidden" name="composites[0][name]" value={selectedSwitch.name} />
								<input type="hidden" name="composites[0][quantity]" value={quantity} />
							{/if}
							{#if selectedKeycap}
								<input type="hidden" name="composites[1][variantId]" value={selectedKeycap.id} />
								<input type="hidden" name="composites[1][name]" value={selectedKeycap.name} />
								<input type="hidden" name="composites[1][quantity]" value={quantity} />
							{/if}
							<Button
								type="submit"
								size="lg"
								class="btn-enhanced min-w-[180px]"
								disabled={!canAddToCart || isAddingToCart}
							>
								{#if isAddingToCart}
									<span
										class="border-primary-foreground mr-2 block h-5 w-5 animate-spin rounded-full border-2 border-t-transparent"
									></span>
									<span>{m.adding()}</span>
								{:else if addedToCart}
									<Check class="mr-2 h-5 w-5" />
									<span>{m.added_to_cart()}</span>
								{:else}
									<ShoppingCart class="mr-2 h-5 w-5" />
									<span>{getButtonText()}</span>
								{/if}
							</Button>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}
