<script lang="ts">
	import type { ProductVariantViewModel } from '$lib/models/product';
	import { Card } from '$lib/components/ui/card';
	import * as m from '$lib/paraglide/messages.js';
	import { formatPrice } from '$lib/utils/price';
	import { getPriceForLocale } from '$lib/utils/price';
	import { Check } from 'lucide-svelte';
	import { AppImage } from '$lib/components/ui/app-image';
	import { fade } from 'svelte/transition';
	import { getLocale } from '$lib/paraglide/runtime';

	let { variant, isSelected, onClick, showPrice, disabled } = $props<{
		variant: ProductVariantViewModel;
		isSelected: boolean;
		onClick: () => void;
		showPrice?: boolean;
		disabled?: boolean;
	}>();

	// Get the current locale
	const currentLocale = $derived(getLocale());

	// Helper function to get variant attribute value
	const getVariantAttribute = (key: string): string => {
		try {
			const value = variant.attributes[key];
			return typeof value === 'string' ? value : '';
		} catch {
			return '';
		}
	};

	// Helper function to translate attribute name
	const getAttributeDisplay = (key: string): string => {
		// Type-safe message object access
		const messages = m as Record<string, unknown>;

		// First try to look up translation using a standardized key format
		const prefixedKey = `variant_attribute_${key}`;

		// Check if the prefixed key exists in the messages object
		if (prefixedKey in messages && typeof messages[prefixedKey] === 'function') {
			// Call the translation function if it exists
			return (messages[prefixedKey] as () => string)();
		}

		// Convert snake_case to camelCase for message lookup
		const camelCaseKey = key.replace(/_([a-z])/g, (g) => g[1].toUpperCase());

		// Check if the camelCase key exists in messages
		if (camelCaseKey in messages && typeof messages[camelCaseKey] === 'function') {
			return (messages[camelCaseKey] as () => string)();
		}

		// Then check if the direct key exists (like 'feel', 'color', etc.)
		if (key in messages && typeof messages[key] === 'function') {
			// Call the translation function if it exists
			return (messages[key] as () => string)();
		}

		// For attributes without translations, format the key name
		return key
			.split('_')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ');
	};

	// Get variant image URL
	const variantImageUrl = $derived(variant.image?.url || '');

	// Check if variant is out of stock
	const isOutOfStock = $derived(variant.stockStatus === 'out_of_stock');

	// Handle click with stock check
	function handleClick() {
		if (!isOutOfStock && !disabled) {
			onClick();
		}
	}
</script>

<Card
	class={`card-hover bg-background group relative flex h-full flex-col rounded-lg p-4 transition-all ${
		isSelected ? 'variant-selected' : ''
	} ${isOutOfStock || disabled ? 'pointer-events-none cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
	onclick={handleClick}
>
	<!-- Stock Status Badge -->
	{#if variant.stockStatus === 'out_of_stock'}
		<div
			class="bg-destructive text-destructive-foreground absolute top-2 left-2 z-10 rounded px-2 py-1 text-xs"
		>
			{m.product_out_of_stock()}
		</div>
	{:else if variant.stockStatus === 'low_stock'}
		<div
			class="bg-warning text-warning-foreground absolute top-2 left-2 z-10 rounded px-2 py-1 text-xs"
		>
			{m.product_low_stock()}
		</div>
	{/if}

	<!-- Variant Visual: Make this much larger -->
	<div class="mb-4 w-full overflow-hidden rounded-md">
		<AppImage
			src={variantImageUrl}
			alt={variant.name}
			width="100%"
			height="auto"
			aspectRatio="1/1"
			className="h-full w-full transform transition-transform duration-300 group-hover:scale-105"
			objectFit="cover"
		/>
	</div>

	<!-- Content Container -->
	<div class="flex flex-grow flex-col">
		<!-- Variant Name -->
		<h3 class="mb-2 leading-tight font-semibold">{variant.name}</h3>

		<!-- Variant Price -->
		{#if showPrice !== false}
			<div class="mb-3 flex items-center justify-between">
				<span class="text-lg font-bold"
					>{formatPrice(getPriceForLocale(variant.prices, currentLocale, 0), currentLocale)}</span
				>
			</div>
		{/if}

		<!-- Selected Attributes -->
		{#if variant.attributes}
			<div class="mt-auto space-y-1 text-sm">
				{#each Object.entries(variant.attributes) as [key, value]}
					{#if !['compatibleWith', 'compatibility'].includes(key) && typeof value === 'string'}
						<div class="grid grid-cols-2 items-center gap-1">
							<span class="text-muted-foreground capitalize">
								{getAttributeDisplay(key)}:
							</span>
							<span class="text-right font-medium">{value}</span>
						</div>
					{/if}
				{/each}
			</div>
		{/if}
	</div>

	<!-- Selected Indicator -->
	{#if isSelected}
		<div class="absolute top-2 right-2" transition:fade={{ duration: 300 }}>
			<div
				class="border-primary bg-background flex h-6 w-6 items-center justify-center rounded-full border-2"
			>
				<Check class="text-primary h-4 w-4" />
			</div>
		</div>
	{/if}
</Card>
