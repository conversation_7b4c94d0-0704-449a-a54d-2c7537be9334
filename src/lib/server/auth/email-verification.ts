import { generateUUID } from '$lib/utils/uuid';
import type { RequestEvent } from '@sveltejs/kit';
import { generateRandomOTP } from './utils';
import { prisma } from '$lib/server/db';
import type { EmailVerificationRequest } from '@prisma/client';
import { sendEmail } from '$lib/server/email/send-email';
import { renderDefaultEmail } from '../email/templates/render-default-email';
import { join } from 'path';

export async function getVerificationRequest(
	id: string,
	userId: string
): Promise<EmailVerificationRequest | null> {
	return await prisma.emailVerificationRequest.findFirst({
		where: {
			id: id,
			userId: userId
		}
	});
}

export async function createVerificationRequest(data: {
	id: string;
	userId: string;
	email: string;
	code: string;
	expiresAt: Date;
}): Promise<EmailVerificationRequest> {
	return await prisma.emailVerificationRequest.create({
		data: data
	});
}

export async function deleteVerificationRequest(id: string): Promise<EmailVerificationRequest> {
	return await prisma.emailVerificationRequest.delete({
		where: {
			id: id
		}
	});
}

export async function createEmailVerificationRequest(
	userId: string,
	email: string
): Promise<EmailVerificationRequest> {
	if (!email) {
		throw new Error('Email is required for verification request');
	}

	const id = generateUUID();
	const code = generateRandomOTP();
	const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

	await createVerificationRequest({
		id,
		userId,
		email,
		code,
		expiresAt
	});

	return {
		id,
		userId,
		email,
		code,
		expiresAt
	};
}

export async function deleteUserEmailVerificationRequest(userId: string): Promise<void> {
	await prisma.emailVerificationRequest.deleteMany({
		where: {
			userId: userId
		}
	});
}

export async function sendVerificationEmail(email: string, code: string): Promise<void> {
	const subject = 'Verify your email address';
	const html = renderDefaultEmail({
		title: 'Email Verification',
		bodyHtml: `
            <p>Your verification code is:</p>
            <div style="font-size:2rem; font-weight:700; letter-spacing:0.2em; color:#fe6702; margin:16px 0;">${code}</div>
            <p>This code will expire in 10 minutes.</p>
        `
	});
	await sendEmail(email, subject, html, {
		from: 'noreply',
		attachments: [
			{
				filename: 'logo-dark.svg',
				path: join(process.cwd(), 'static', 'logo-dark.svg'),
				cid: 'logo-dark'
			}
		]
	});
}

export function setEmailVerificationRequestCookie(
	event: RequestEvent,
	request: EmailVerificationRequest
): void {
	event.cookies.set('email_verification', request.id, {
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		maxAge: 60 * 10, // 10 minutes
		domain: '', // Explicitly set empty domain for same-origin only
		path: '/',
		sameSite: 'lax'
	});
}

export function deleteEmailVerificationRequestCookie(event: RequestEvent): void {
	event.cookies.set('email_verification', '', {
		httpOnly: true,
		secure: process.env.NODE_ENV === 'production',
		expires: new Date(0),
		domain: '', // Explicitly set empty domain for same-origin only
		path: '/',
		sameSite: 'lax'
	});
}

export async function getUserEmailVerificationRequestFromRequest(
	event: RequestEvent
): Promise<EmailVerificationRequest | null> {
	console.log('Getting user email verification request from request');
	if (event.locals.user === null) {
		return null;
	}
	console.log('User is authenticated');
	const id = event.cookies.get('email_verification') ?? null;
	if (id === null) {
		return null;
	}
	console.log('ID is not null');
	const request = await getVerificationRequest(id, event.locals.user.id);
	if (!request) {
		console.log('Request is null');
		deleteEmailVerificationRequestCookie(event);
		return null;
	}
	return request;
}
