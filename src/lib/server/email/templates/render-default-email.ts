export function renderDefaultEmail({
	title,
	bodyHtml,
	ctaLabel,
	ctaUrl
}: {
	title: string;
	bodyHtml: string;
	ctaLabel?: string;
	ctaUrl?: string;
}) {
	return `
  <!DOCTYPE html>
  <html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>${title}</title>
    <style>
      body { background: #f6f8fa; color: #232323; font-family: 'Inter', Arial, sans-serif; margin: 0; }
      .container { max-width: 480px; margin: 32px auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 12px #0001; padding: 32px 24px; }
      .header { text-align: center; margin-bottom: 24px; }
      .logo { max-width: 120px; margin: 0 auto 12px; }
      .title { font-size: 1.5rem; font-weight: 700; color: #fe6702; margin-bottom: 8px; }
      .content { font-size: 1rem; color: #232323; margin-bottom: 24px; }
      .cta { display: inline-block; background: #fe6702; color: #fff; padding: 12px 28px; border-radius: 6px; text-decoration: none; font-weight: 600; margin-top: 16px; }
      .footer { text-align: center; color: #888; font-size: 0.85rem; margin-top: 32px; }
      @media (max-width: 600px) { .container { padding: 16px 4px; } }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <img src="cid:logo-dark" alt="ONSPRY Logo" class="logo" />
        <div class="title">${title}</div>
      </div>
      <div class="content">
        ${bodyHtml}
        ${ctaLabel && ctaUrl ? `<div style="text-align:center;"><a href="${ctaUrl}" class="cta">${ctaLabel}</a></div>` : ''}
      </div>
      <div class="footer">
        &copy; ${new Date().getFullYear()} ONSPRY. All rights reserved.
      </div>
    </div>
  </body>
  </html>
  `;
}
