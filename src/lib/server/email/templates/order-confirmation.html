<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Order Confirmation</title>
		<style>
			body {
				background-color: #fff;
				color: #232323;
				font-family: 'Inter', Arial, sans-serif;
			}

			.container {
				max-width: 600px;
				margin: 0 auto;
				padding: 20px;
			}

			.header {
				border-bottom: 4px solid #fe6702;
				text-align: center;
				padding: 20px 0;
			}

			.header-logo {
				max-width: 180px;
				display: block;
				margin: 0 auto;
				background: #fff;
				border-radius: 8px;
				box-shadow: 0 1px 4px rgba(0, 0, 0, 0.07);
				padding: 8px;
			}

			.header-title {
				font-family: 'Onest', Arial, sans-serif;
				font-weight: 700;
				font-size: 2.25rem;
				margin: 1rem 0 0.5rem 0;
				color: #232323;
			}

			.header-subtitle {
				font-family: 'Inter', <PERSON><PERSON>, sans-serif;
				font-size: 1.125rem;
				color: #444;
				margin: 0;
			}

			.content {
				padding: 20px 0;
			}

			.order-details {
				background-color: #fff;
				border: 1px solid #e5e7eb;
				border-radius: 8px;
				margin: 20px 0;
				padding: 20px;
			}

			.order-item {
				margin: 10px 0;
				padding: 10px;
			}

			.footer {
				text-align: center;
				padding: 20px 0;
				font-size: 12px;
				color: #6c757d;
				border-top: 1px solid #dee2e6;
			}
		</style>
	</head>

	<body>
		<div class="container">
			<div class="header">
				<img src="cid:logo-dark" alt="ONSPRY Logo" class="header-logo" />
				<h1 class="header-title">Order Confirmation</h1>
				<p class="header-subtitle">Thank you for your order!</p>
			</div>

			<div class="content">
				<p>Dear {customerName},</p>

				<p>
					Thank you for your order. We're pleased to confirm that we've received your order and it's
					being processed.
				</p>

				<div class="order-details">
					<h2>Order Details</h2>
					<p><strong>Order Number:</strong> {orderNumber}</p>
					<p><strong>Order Date:</strong> {orderDate}</p>

					<h3>Shipping Information</h3>
					<p>{shippingAddress}</p>

					<h3>Items Ordered:</h3>
					{orderItems}

					<div class="order-summary">
						<h3>Order Summary</h3>
						<p><strong>Subtotal:</strong> {subtotal}</p>
						<p><strong>Shipping:</strong> {shippingAmount}</p>
						<p><strong>Tax:</strong> {taxAmount}</p>
						{discountAmount}
						<p><strong>Total:</strong> {total}</p>
					</div>
				</div>

				<p>We'll send you another email when your order ships.</p>
			</div>

			<div class="footer">
				<p>If you have any questions about your order, please contact our customer service team.</p>
				<p>&copy; {currentYear} Your Company Name. All rights reserved.</p>
			</div>
		</div>
	</body>
</html>
