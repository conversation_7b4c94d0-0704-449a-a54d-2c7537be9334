import { join } from 'path';
import { sendEmail } from './send-email';
import type { OrderViewModel } from '$lib/models/order';
import { formatPrice } from '$lib/utils/price';
import { renderDefaultEmail } from './templates/render-default-email';
import type { Locale } from '$lib/utils/localization';

function validateShippingAddress(address: OrderViewModel['shippingAddress']): void {
	if (!address) {
		throw new Error('Shipping address is required');
	}

	const requiredFields = [
		{ field: 'firstName', value: address.firstName },
		{ field: 'lastName', value: address.lastName },
		{ field: 'address1', value: address.address1 },
		{ field: 'city', value: address.city },
		{ field: 'postalCode', value: address.postalCode },
		{ field: 'country', value: address.country },
		{ field: 'email', value: address.email }
	];

	for (const { field, value } of requiredFields) {
		if (!value || typeof value !== 'string' || value.trim() === '') {
			throw new Error(`Shipping address ${field} is required`);
		}
	}

	// State is optional, but if provided must be a string
	if (address.state !== undefined && address.state !== null && typeof address.state !== 'string') {
		throw new Error('Shipping address state must be a string if provided');
	}
}

export async function sendOrderConfirmationEmail(order: OrderViewModel): Promise<void> {
	try {
		// Validate shipping address
		validateShippingAddress(order.shippingAddress);

		// Cast order locale to proper type for formatPrice
		const orderLocale = order.locale as Locale;

		// Format the order items with proper locale/currency
		const orderItemsHtml = order.items
			.map(
				(item) => `
            <div style="margin-bottom:8px;">
                <div><strong>${item.name}</strong> <span style="color:#888;">${item.variantName ? `- ${item.variantName}` : ''}</span></div>
                <div style="font-size:0.95em; color:#555;">Quantity: ${item.quantity} &bull; ${formatPrice(item.totalPrice, orderLocale)}</div>
            </div>
        `
			)
			.join('');

		// No discount functionality - removed
		const discountAmountHtml = '';

		// Format the shipping address
		const shippingAddressHtml = `
            <div>
                ${order.shippingAddress.firstName} ${order.shippingAddress.lastName}<br/>
                ${order.shippingAddress.address1}<br/>
                ${order.shippingAddress.address2 ? `${order.shippingAddress.address2}<br/>` : ''}
                ${order.shippingAddress.city}${order.shippingAddress.state ? `, ${order.shippingAddress.state}` : ''} ${order.shippingAddress.postalCode}<br/>
                ${order.shippingAddress.country}
            </div>
        `;

		// Build the email body
		const bodyHtml = `
            <p>Hi ${order.shippingAddress.firstName},</p>
            <p>Thank you for your order! We're pleased to confirm that we've received your order and it's being processed.</p>
            <div style="margin:24px 0; padding:16px; background:#f6f8fa; border-radius:8px;">
                <div style="font-size:1.1em; font-weight:600; margin-bottom:8px;">Order #${order.orderNumber}</div>
                <div style="color:#888; margin-bottom:8px;">Placed on ${new Date(order.createdAt).toLocaleDateString()}</div>
                <div style="margin-bottom:8px;">${shippingAddressHtml}</div>
                <div style="margin-top:16px;">
                    <div style="font-weight:600; margin-bottom:4px;">Items Ordered:</div>
                    ${orderItemsHtml}
                </div>
                <div style="margin-top:16px;">
                    <div>Subtotal: ${formatPrice(order.subtotal, orderLocale)}</div>
                    <div>Shipping: ${formatPrice(order.shippingAmount, orderLocale)}</div>
                    ${order.taxAmount > 0 ? `<div>${order.taxRate ? `VAT (${(order.taxRate * 100).toFixed(1)}%) - ${order.shippingAddress?.country || 'Unknown'} included` : `Tax - ${order.shippingAddress?.country || 'Unknown'} included`}: ${formatPrice(order.taxAmount, orderLocale)}</div>` : ''}
                    ${discountAmountHtml}
                    <div style="font-weight:600; margin-top:8px;">Total: ${formatPrice(order.total, orderLocale)}</div>
                </div>
            </div>
            <p>We'll send you another email when your order ships.</p>
        `;

		// Use the new default template
		const emailContent = renderDefaultEmail({
			title: 'Order Confirmation',
			bodyHtml
		});

		// Send the email
		await sendEmail(
			order.shippingAddress.email,
			`Order Confirmation - ${order.orderNumber}`,
			emailContent,
			{
				from: 'noreply',
				attachments: [
					{
						filename: 'logo-dark.svg',
						path: join(process.cwd(), 'static', 'logo-dark.svg'),
						cid: 'logo-dark'
					}
				]
			}
		);
	} catch (error) {
		console.error('Error sending order confirmation email:', error);
		throw new Error('Failed to send order confirmation email');
	}
}
