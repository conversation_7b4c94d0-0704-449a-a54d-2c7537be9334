import { getDisplayCurrency } from '$lib/utils/price';
import type { Locale } from '$lib/utils/localization';

/**
 * Gets the appropriate currency for UI display
 * This decouples UI language from currency display
 */
export function getUICurrency(userLanguage: Locale, shippingCountry?: string): string {
	return getDisplayCurrency(userLanguage, shippingCountry);
}

/**
 * Reactive store for display currency that updates when shipping country changes
 */
import { writable, derived } from 'svelte/store';

// Store for current shipping country (updated from checkout)
export const shippingCountryStore = writable<string | undefined>(undefined);

// Store for user language
export const userLanguageStore = writable<Locale>('en-US');

// Derived store for display currency
export const displayCurrencyStore = derived(
	[userLanguageStore, shippingCountryStore],
	([userLanguage, shippingCountry]) => {
		return getDisplayCurrency(userLanguage, shippingCountry);
	}
);

/**
 * Updates the shipping country and triggers currency recalculation
 */
export function updateShippingCountry(country: string | undefined) {
	shippingCountryStore.set(country);
}

/**
 * Updates the user language
 */
export function updateUserLanguage(language: Locale) {
	userLanguageStore.set(language);
}
