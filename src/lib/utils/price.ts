import type { Locale } from '$lib/utils/localization';

/**
 * Type definition for supported currencies
 */
export type SupportedCurrency = 'EUR' | 'GBP' | 'CNY' | 'USD';

/**
 * Regional currency zones - groups languages/countries by business regions
 */
export const regionalCurrencyZones = {
	european: {
		languages: ['de-DE', 'fr-FR', 'it-IT', 'es-ES', 'nl-NL'],
		countries: ['DE', 'FR', 'IT', 'ES', 'AT', 'NL', 'BE', 'PT', 'IE', 'FI', 'LU'],
		currency: 'EUR' as SupportedCurrency
	},
	uk: {
		languages: ['en-UK'],
		countries: ['GB', 'UK'],
		currency: 'GBP' as SupportedCurrency
	},
	north_american: {
		languages: ['en-US'],
		countries: ['US', 'CA'],
		currency: 'USD' as SupportedCurrency
	},
	asian: {
		languages: ['zh-CN'],
		countries: ['CN', 'HK', 'TW', 'SG'],
		currency: 'CNY' as SupportedCurrency
	}
};

/**
 * Maps locale to currency (legacy - kept for backward compatibility)
 */
export const localeToCurrency: Record<string, SupportedCurrency> = {
	'en-US': 'USD',
	'en-UK': 'GBP',
	'de-DE': 'EUR',
	'fr-FR': 'EUR',
	'zh-CN': 'CNY'
};

/**
 * Default currency to use if none specified
 */
export const DEFAULT_CURRENCY: SupportedCurrency = 'USD';

/**
 * Gets the currency for a specific locale (legacy)
 */
export function getCurrencyForLocale(locale: Locale): SupportedCurrency {
	return localeToCurrency[locale] || DEFAULT_CURRENCY;
}

/**
 * Gets the currency for a specific country
 */
export function getCurrencyForCountry(country: string): SupportedCurrency {
	for (const zone of Object.values(regionalCurrencyZones)) {
		if (zone.countries.includes(country)) {
			return zone.currency;
		}
	}
	return DEFAULT_CURRENCY;
}

/**
 * Gets the regional currency based on user language (for early display)
 */
export function getRegionalCurrency(locale: Locale): SupportedCurrency {
	for (const zone of Object.values(regionalCurrencyZones)) {
		if (zone.languages.includes(locale)) {
			return zone.currency;
		}
	}
	return DEFAULT_CURRENCY;
}

/**
 * Gets the display currency - uses shipping country if available, otherwise regional default
 */
export function getDisplayCurrency(userLanguage: Locale, shippingCountry?: string): SupportedCurrency {
	// Priority 1: Use shipping destination currency if known
	if (shippingCountry) {
		return getCurrencyForCountry(shippingCountry);
	}

	// Priority 2: Use regional currency based on language
	return getRegionalCurrency(userLanguage);
}

/**
 * Gets the price for a specific locale from a prices object
 * @param prices - Object containing prices in different currencies
 * @param locale - Target locale
 * @param fallback - Fallback price if locale not found
 * @returns Price in cents for the locale
 */
export function getPriceForLocale(
	prices: Record<string, number> | null | undefined,
	locale: Locale,
	fallback: number = 0
): number {
	if (!prices || typeof prices !== 'object') {
		return fallback;
	}

	const currency = getCurrencyForLocale(locale);
	return prices[currency] ?? fallback;
}

/**
 * Formats a price from cents to a localized currency string
 * @param price - Price in cents (integer)
 * @param locale - Locale for formatting (defaults to 'en-US')
 * @returns Formatted price string with currency symbol
 */
export function formatPrice(price: number, locale: Locale = 'en-US'): string {
	// Get currency based on locale
	const currency = getCurrencyForLocale(locale);

	// Format the price with the correct locale and currency
	const dollars = price / 100;
	return new Intl.NumberFormat(locale, {
		style: 'currency',
		currency,
		minimumFractionDigits: 2,
		maximumFractionDigits: 2
	}).format(dollars);
}

/**
 * Converts a price from dollars to cents
 * @param price - Price in dollars (float)
 * @returns Price in cents (integer)
 */
export function toCents(price: number): number {
	return Math.round(price * 100);
}

/**
 * Converts a price from cents to dollars
 * @param price - Price in cents (integer)
 * @returns Price in dollars (float)
 */
export function toDollars(price: number): number {
	return price / 100;
}

/**
 * Calculates the total price from an array of prices in cents
 * @param prices - Array of prices in cents
 * @returns Total price in cents
 */
export function calculateTotal(prices: number[]): number {
	return prices.reduce((sum, price) => sum + price, 0);
}
