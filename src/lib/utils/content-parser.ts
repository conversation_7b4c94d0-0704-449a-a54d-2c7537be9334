import { localizeHref } from '$lib/paraglide/runtime';

/**
 * Interface for parsed content elements
 */
export interface ContentElement {
	type: 'paragraph' | 'list' | 'link' | 'text' | 'strong' | 'em' | 'code' | 'blockquote' | 'image';
	content?: string;
	href?: string;
	target?: string;
	rel?: string;
	alt?: string;
	src?: string;
	children?: ContentElement[];
}

/**
 * Interface for a single content section with support for nesting
 */
export interface ContentSection {
	title: string;
	content: ContentElement[];
	level: number;
	children: ContentSection[];
}

/**
 * Interface for the complete structured content
 */
export interface StructuredContent {
	title: string;
	intro: ContentElement[];
	sections: ContentSection[];
}

/**
 * Decodes HTML entities to their corresponding characters
 * @param text Text containing HTML entities
 * @returns Decoded text
 */
function decodeHtmlEntities(text: string): string {
	const entityMap: Record<string, string> = {
		'&amp;': '&',
		'&lt;': '<',
		'&gt;': '>',
		'&quot;': '"',
		'&#39;': "'",
		'&#8217;': "'", // Right single quotation mark
		'&#8216;': "'", // Left single quotation mark
		'&#8220;': '"', // Left double quotation mark
		'&#8221;': '"', // Right double quotation mark
		'&#8211;': '–', // En dash
		'&#8212;': '—', // Em dash
		'&#8230;': '…', // Horizontal ellipsis
		'&nbsp;': ' ',
		'&hellip;': '…',
		'&mdash;': '—',
		'&ndash;': '–',
		'&lsquo;': '\u2018',
		'&rsquo;': '\u2019',
		'&ldquo;': '\u201C',
		'&rdquo;': '\u201D'
	};

	let decoded = text;

	// Replace named entities
	for (const [entity, char] of Object.entries(entityMap)) {
		decoded = decoded.replace(new RegExp(entity, 'g'), char);
	}

	// Replace numeric entities (&#123; format)
	decoded = decoded.replace(/&#(\d+);/g, (_match, num) => {
		return String.fromCharCode(parseInt(num, 10));
	});

	// Replace hex entities (&#x1F; format)
	decoded = decoded.replace(/&#x([0-9A-Fa-f]+);/g, (_match, hex) => {
		return String.fromCharCode(parseInt(hex, 16));
	});

	return decoded;
}

/**
 * Parses HTML content from markdown into structured sections with nested hierarchy
 * @param html HTML content generated from markdown
 * @returns Structured content with title, intro, and nested sections
 */
export function parseStructuredContent(html: string): StructuredContent {
	// Extract the main title (h1)
	let title = 'Page Title';
	const titleMatch = html.match(/<h1>(.*?)<\/h1>/);
	if (titleMatch && titleMatch[1]) {
		title = decodeHtmlEntities(titleMatch[1]);
	}

	// Remove the title from content
	let contentWithoutTitle = html.replace(/<h1>.*?<\/h1>/, '').trim();

	// Extract intro paragraph (content before first heading)
	let intro: ContentElement[] = [];
	const introMatch = contentWithoutTitle.match(/^(.*?)(?=<h[2-6]>|$)/s);
	if (introMatch && introMatch[1]) {
		const introHtml = introMatch[1].trim();
		intro = parseHtmlToElements(introHtml);
		contentWithoutTitle = contentWithoutTitle.replace(introMatch[1], '').trim();
	}

	// Find all heading tags (h2, h3, h4, h5, h6) and their content
	const headingPattern = /<h([2-6])>(.*?)<\/h\1>(.*?)(?=<h[2-6]>|$)/gs;
	const headings: { level: number; title: string; content: ContentElement[]; index: number }[] = [];

	let match;
	while ((match = headingPattern.exec(contentWithoutTitle)) !== null) {
		const level = parseInt(match[1], 10);
		const title = decodeHtmlEntities(match[2].trim());
		const contentHtml = match[3].trim();
		const content = parseHtmlToElements(contentHtml);

		headings.push({
			level,
			title,
			content,
			index: match.index
		});
	}

	// Build the hierarchical structure
	const rootSections: ContentSection[] = [];
	const sectionStack: ContentSection[] = [];

	headings.forEach((heading) => {
		const section: ContentSection = {
			title: heading.title,
			content: heading.content,
			level: heading.level,
			children: []
		};

		// Find the appropriate parent based on heading level
		while (
			sectionStack.length > 0 &&
			sectionStack[sectionStack.length - 1].level >= heading.level
		) {
			sectionStack.pop();
		}

		if (sectionStack.length === 0) {
			// This is a top-level section
			rootSections.push(section);
		} else {
			// This is a child section
			sectionStack[sectionStack.length - 1].children.push(section);
		}

		// Push this section to the stack for potential children
		sectionStack.push(section);
	});

	return {
		title,
		intro,
		sections: rootSections
	};
}

/**
 * Parses HTML content into structured ContentElement array
 * @param html HTML content to parse
 * @returns Array of ContentElement objects
 */
function parseHtmlToElements(html: string): ContentElement[] {
	const elements: ContentElement[] = [];

	// Simple HTML parser - handles common markdown-generated elements
	const htmlWithLocalizedLinks = localizeInternalHrefs(convertUrlsToLinks(html));

	// Split by major block elements
	const blockPattern = /<(p|ul|ol|blockquote|pre|h[1-6])(?:[^>]*)>(.*?)<\/\1>/gs;
	let lastIndex = 0;
	let match;

	while ((match = blockPattern.exec(htmlWithLocalizedLinks)) !== null) {
		// Add any text before this match
		const beforeText = htmlWithLocalizedLinks.slice(lastIndex, match.index).trim();
		if (beforeText) {
			elements.push(...parseInlineElements(beforeText));
		}

		const tagName = match[1];
		const content = match[2];

		switch (tagName) {
			case 'p':
				elements.push({
					type: 'paragraph',
					children: parseInlineElements(content)
				});
				break;
			case 'ul':
			case 'ol':
				elements.push({
					type: 'list',
					children: parseListItems(content)
				});
				break;
			case 'blockquote':
				elements.push({
					type: 'blockquote',
					children: parseInlineElements(content)
				});
				break;
			default:
				// For other elements, treat as paragraph
				elements.push({
					type: 'paragraph',
					children: parseInlineElements(content)
				});
		}

		lastIndex = match.index + match[0].length;
	}

	// Add any remaining text
	const remainingText = htmlWithLocalizedLinks.slice(lastIndex).trim();
	if (remainingText) {
		elements.push(...parseInlineElements(remainingText));
	}

	return elements;
}

/**
 * Parses inline HTML elements (links, strong, em, code, etc.)
 * @param html HTML content containing inline elements
 * @returns Array of ContentElement objects
 */
function parseInlineElements(html: string): ContentElement[] {
	const elements: ContentElement[] = [];

	// Pattern for inline elements
	const inlinePattern = /<(a|strong|em|code|img)(?:\s+([^>]*))?>([^<]*)<\/\1>|([^<]+)/g;
	let match;

	while ((match = inlinePattern.exec(html)) !== null) {
		if (match[1]) {
			// This is an HTML element
			const tagName = match[1];
			const attributes = match[2] || '';
			const content = match[3] || '';

			switch (tagName) {
				case 'a': {
					const hrefMatch = attributes.match(/href=["']([^"']+)["']/);
					const targetMatch = attributes.match(/target=["']([^"']+)["']/);
					const relMatch = attributes.match(/rel=["']([^"']+)["']/);

					elements.push({
						type: 'link',
						content: decodeHtmlEntities(content),
						href: hrefMatch ? hrefMatch[1] : '#',
						target: targetMatch ? targetMatch[1] : undefined,
						rel: relMatch ? relMatch[1] : undefined
					});
					break;
				}
				case 'strong':
					elements.push({
						type: 'strong',
						content: decodeHtmlEntities(content)
					});
					break;
				case 'em':
					elements.push({
						type: 'em',
						content: decodeHtmlEntities(content)
					});
					break;
				case 'code':
					elements.push({
						type: 'code',
						content: decodeHtmlEntities(content)
					});
					break;
				case 'img': {
					const srcMatch = attributes.match(/src=["']([^"']+)["']/);
					const altMatch = attributes.match(/alt=["']([^"']+)["']/);

					elements.push({
						type: 'image',
						src: srcMatch ? srcMatch[1] : '',
						alt: altMatch ? decodeHtmlEntities(altMatch[1] || '') : ''
					});
					break;
				}
			}
		} else if (match[4]) {
			// This is plain text
			const text = match[4].trim();
			if (text) {
				elements.push({
					type: 'text',
					content: decodeHtmlEntities(text)
				});
			}
		}
	}

	return elements;
}

/**
 * Parses list items from HTML
 * @param html HTML content containing list items
 * @returns Array of ContentElement objects representing list items
 */
function parseListItems(html: string): ContentElement[] {
	const items: ContentElement[] = [];
	const itemPattern = /<li(?:[^>]*)>(.*?)<\/li>/gs;
	let match;

	while ((match = itemPattern.exec(html)) !== null) {
		const content = match[1].trim();
		items.push({
			type: 'paragraph', // List items are treated as paragraphs
			children: parseInlineElements(content)
		});
	}

	return items;
}

/**
 * Applies localization to internal hrefs in HTML content
 * @param content HTML content that may contain link elements
 * @returns HTML content with localized internal hrefs
 */
function localizeInternalHrefs(content: string): string {
	// Match <a> tags with href attribute that points to internal paths
	// but exclude links that are already external (http://, https://, mailto:, etc.)
	const internalLinkRegex =
		/<a\s+(?:[^>]*?\s+)?href=["'](?!https?:\/\/|mailto:|tel:|#)([^'"]+)["']([^>]*)>/gi;

	// Replace internal hrefs with localized versions - clean link styling
	return content.replace(internalLinkRegex, (_match, path, rest) => {
		// Make sure path starts with / for localizeHref
		const internalPath = path.startsWith('/') ? path : `/${path}`;
		return `<a href="${localizeHref(internalPath)}"${rest}>`;
	});
}

/**
 * Converts plain text URLs to HTML anchor tags
 * @param content HTML content that may contain plain URLs
 * @returns HTML content with URLs converted to links
 */
function convertUrlsToLinks(content: string): string {
	// First, handle plain text URLs that aren't already in HTML links
	// This matches URLs starting with http://, https://, or www.
	const urlRegex = /(?<!["'=])(https?:\/\/|www\.)[^\s<>]+\.[^\s<>]+(?!["'>])/g;

	// Replace plain URLs with HTML links - clean external link styling
	let processedContent = content.replace(urlRegex, (url) => {
		// Add https:// prefix if the URL starts with www.
		const fullUrl = url.startsWith('www.') ? `https://${url}` : url;
		return `<a href="${fullUrl}" target="_blank" rel="noopener noreferrer">${url}</a>`;
	});

	// Clean image styling - simple and elegant
	processedContent = processedContent.replace(
		/<img([^>]*)>/g,
		'<img$1 class="rounded-lg max-w-full h-auto my-6 shadow-sm">'
	);

	// Clean blockquote styling - minimal left border like Svelte blog
	processedContent = processedContent.replace(
		/<blockquote([^>]*)>/g,
		'<blockquote$1 class="border-l-2 border-muted-foreground/20 pl-6 my-6 italic text-muted-foreground">'
	);

	// Clean code styling - simple and readable
	processedContent = processedContent.replace(
		/<code([^>]*)>/g,
		'<code$1 class="bg-muted/50 px-1.5 py-0.5 rounded text-sm font-mono">'
	);

	// Clean pre and code block styling
	processedContent = processedContent.replace(
		/<pre([^>]*)>/g,
		'<pre$1 class="bg-muted/30 border border-muted rounded-lg p-4 my-6 overflow-x-auto">'
	);
	processedContent = processedContent.replace(
		/<pre[^>]*><code([^>]*)>/g,
		'<pre class="bg-muted/30 border border-muted rounded-lg p-4 my-6 overflow-x-auto"><code$1 class="bg-transparent p-0 text-sm font-mono">'
	);

	// Clean table styling - simple borders
	processedContent = processedContent.replace(
		/<table([^>]*)>/g,
		'<div class="overflow-x-auto my-6"><table$1 class="w-full border-collapse border border-muted rounded-lg">'
	);
	processedContent = processedContent.replace(/<\/table>/g, '</table></div>');
	processedContent = processedContent.replace(
		/<th([^>]*)>/g,
		'<th$1 class="border-b border-muted bg-muted/20 px-4 py-3 text-left font-medium">'
	);
	processedContent = processedContent.replace(
		/<td([^>]*)>/g,
		'<td$1 class="border-b border-muted px-4 py-3">'
	);

	// Clean list styling - using CSS from app.css
	processedContent = processedContent.replace(/<ul([^>]*)>/g, '<ul$1 class="my-6 space-y-2">');
	processedContent = processedContent.replace(
		/<ol([^>]*)>/g,
		'<ol$1 class="my-6 space-y-2 list-decimal list-inside">'
	);
	processedContent = processedContent.replace(/<li([^>]*)>/g, '<li$1 class="leading-relaxed">');

	// Add better spacing for paragraphs
	processedContent = processedContent.replace(/<p([^>]*)>/g, '<p$1 class="my-4 leading-relaxed">');

	// Clean heading styling within content - only add spacing, let global styles handle typography
	processedContent = processedContent.replace(
		/<h2([^>]*)>/g,
		'<h2$1 class="mt-12 mb-4 first:mt-0">'
	);
	processedContent = processedContent.replace(
		/<h3([^>]*)>/g,
		'<h3$1 class="mt-10 mb-3 first:mt-0">'
	);
	processedContent = processedContent.replace(
		/<h4([^>]*)>/g,
		'<h4$1 class="mt-8 mb-3 first:mt-0">'
	);
	processedContent = processedContent.replace(
		/<h5([^>]*)>/g,
		'<h5$1 class="mt-6 mb-2 first:mt-0">'
	);
	processedContent = processedContent.replace(
		/<h6([^>]*)>/g,
		'<h6$1 class="mt-6 mb-2 first:mt-0">'
	);

	return processedContent;
}
