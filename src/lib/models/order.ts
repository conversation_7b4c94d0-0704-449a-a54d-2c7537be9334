export const OrderStatus = {
	PENDING_PAYMENT: 'pending_payment',
	PAYMENT_FAILED: 'payment_failed',
	PROCESSING: 'processing',
	SHIPPED: 'shipped',
	DELIVERED: 'delivered',
	CANCELLED: 'cancelled',
	REFUNDED: 'refunded'
} as const;

export type OrderStatus = (typeof OrderStatus)[keyof typeof OrderStatus];

export const PaymentStatus = {
	PENDING: 'pending',
	SUCCEEDED: 'succeeded',
	FAILED: 'failed',
	REFUNDED: 'refunded'
} as const;

export type PaymentStatus = (typeof PaymentStatus)[keyof typeof PaymentStatus];

export const RefundStatus = {
	PENDING: 'pending',
	PROCESSING: 'processing',
	COMPLETED: 'completed',
	FAILED: 'failed'
} as const;

export type RefundStatus = (typeof RefundStatus)[keyof typeof RefundStatus];

/**
 * Represents a composite item in an order
 */
export interface OrderItemCompositeViewModel {
	variantId: string;
	name: string;
	quantity: number;
	type?: string; // For categorizing composites (e.g., 'SWITCH', 'KEYCAP')
}

/**
 * Represents an item in an order
 */
export interface OrderItemViewModel {
	productId: string;
	variantId: string;
	quantity: number;

	// Price breakdown
	unitPrice: number; // Unit price before tax
	unitPriceWithTax: number; // Unit price including tax
	totalPrice: number; // Total line price (unitPriceWithTax * quantity)
	taxAmount: number; // Total tax for this line item
	taxRate?: number; // Tax rate applied

	// Product information snapshot
	productName: string;
	variantName: string;
	sku?: string;
	category?: string;

	// Pricing metadata
	currency: string;
	originalPrices?: Record<string, number>; // Original multi-currency prices

	// Product composition
	composites?: OrderItemCompositeViewModel[];
}

/**
 * Represents a shipping or billing address
 */
export interface OrderAddressViewModel {
	firstName: string;
	lastName: string;
	address1: string;
	address2?: string;
	city: string;
	state: string;
	postalCode: string;
	country: string;
	email: string;
	phone?: string;
}

/**
 * Represents payment information
 */
export interface OrderPaymentViewModel {
	method: string;
	intentId: string;
	cardLast4?: string;
	cardBrand?: string;
}

/**
 * Tax breakdown information for compliance and reporting
 */
export interface OrderTaxBreakdownViewModel {
	jurisdiction: string;
	jurisdictionType: string;
	jurisdictionName: string;
	taxType: string;
	taxRate: number;
	taxableAmount: number;
	taxAmount: number;
	calculationMethod?: string;
	isInclusive: boolean;
}

/**
 * Detailed shipping information
 */
export interface OrderShippingViewModel {
	method: string;
	carrier?: string;
	service?: string;
	baseAmount: number;
	taxAmount: number;
	totalAmount: number;
	taxRate?: number;
	taxJurisdiction?: string;
	estimatedDays?: number;
	trackingNumber?: string;
	shippingCountry: string;
	shippingState?: string;
	shippingCity: string;
	shippingPostal: string;
}

/**
 * Data required to create a new order
 */
export interface CreateOrderViewModel {
	userId?: string;
	cartId: string;
	items: OrderItemViewModel[];
	shipping: {
		method: string;
		amount: number;
		taxAmount?: number;
		address: OrderAddressViewModel;
		carrier?: string;
		service?: string;
		estimatedDays?: number;
	};
	payment: OrderPaymentViewModel;
	subtotal: number;
	taxAmount: number;
	taxRate?: number;
	shippingAmount: number;
	currency: string;
	locale: string;
}

/**
 * Complete order data including database fields
 */
export interface OrderViewModel {
	id: string;
	orderNumber: string; // Formatted order number for display
	status: OrderStatus | string;

	// Price breakdown
	total: number;
	subtotal: number;
	taxAmount: number;
	taxRate?: number;
	shippingAmount: number;

	// Currency and locale
	currency: string;
	locale: string;
	exchangeRate?: number;

	// Shipping and payment
	shippingMethod: string;
	paymentMethod: string;

	// Timestamps
	createdAt: string;

	// Related data
	items: Array<{
		id: string;
		productId: string;
		variantId?: string;
		quantity: number;
		unitPrice: number;
		unitPriceWithTax: number;
		totalPrice: number;
		taxAmount: number;
		taxRate?: number;
		name: string;
		variantName: string;
		sku?: string;
		category?: string;
		currency: string;
		composites?: OrderItemCompositeViewModel[];
	}>;

	shippingAddress: {
		firstName: string;
		lastName: string;
		address1: string;
		address2?: string;
		city: string;
		state: string;
		postalCode: string;
		country: string;
		phone?: string;
		email: string;
	};

	shippingDetails?: OrderShippingViewModel;
}
