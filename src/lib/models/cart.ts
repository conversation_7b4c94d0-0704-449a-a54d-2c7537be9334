import type { ProductVariantViewModel } from './product';

export interface CartItemCompositeViewModel {
	variantId: string;
	name: string;
	quantity: number;
	variant: ProductVariantViewModel;
}

/**
 * Represents an item in the cart with a fully normalized structure
 */
export interface CartItemViewModel {
	id: string;
	quantity: number;
	prices: Record<string, number>; // Copy of variant prices when added to cart: { "EUR": 10000, "GBP": 8500, "CNY": 78000 }
	// The variant contains all the product information we need
	variant: ProductVariantViewModel;
	// For convenience in UI display
	imageUrl: string;
	// For composite products
	composites: CartItemCompositeViewModel[];
}

export interface CartViewModel {
	id: string;
	items: CartItemViewModel[];
	subtotal: number;
	total: number;
	itemCount: number;
}

export interface AddToCartPayload {
	productVariantId: string;
	quantity: number;
	composites?: Array<{
		variantId: string;
		name: string;
		quantity: number;
	}>;
}

export interface UpdateCartItemPayload {
	cartItemId: string;
	quantity: number;
	composites?: Array<{
		variantId: string;
		name: string;
		quantity: number;
	}>;
}

export interface RemoveCartItemPayload {
	cartItemId: string;
}

export interface CartSummaryViewModel {
	subtotal: number;
	total: number;
	itemCount: number;
}

export enum CartStatus {
	ACTIVE = 'active',
	MERGED = 'merged',
	CONVERTED_TO_ORDER = 'converted_to_order',
	ABANDONED = 'abandoned'
}
