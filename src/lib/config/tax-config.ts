/**
 * Tax configuration for international e-commerce
 * Based on destination-based taxation (ship-to address)
 */

export interface TaxRule {
	/** Tax rate as decimal (e.g., 0.08 for 8%) */
	rate: number;
	/** Display name for the tax */
	name: string;
	/** Whether tax is included in product prices */
	inclusive: boolean;
	/** Minimum order value to apply tax (in cents) */
	threshold?: number;
	/** Whether to apply tax to shipping */
	applyToShipping: boolean;
	/** Whether to apply tax to digital goods */
	applyToDigital: boolean;
}

export interface CountryTaxConfig {
	/** Default tax rule for the country */
	default: TaxRule;
	/** State/province specific tax rules */
	regions?: Record<string, TaxRule>;
	/** Special rules for business customers */
	business?: TaxRule;
}

/**
 * Tax configuration by country code
 * Based on 2024 tax rates - should be updated regularly
 */
export const TAX_CONFIG: Record<string, CountryTaxConfig> = {
	// United States - State sales tax (included in our prices)
	US: {
		default: {
			rate: 0.0, // No federal sales tax
			name: 'Sales Tax',
			inclusive: true, // Our prices include any applicable tax
			applyToShipping: false,
			applyToDigital: true
		},
		regions: {
			// Major states with sales tax (included in our prices)
			CA: { rate: 0.0725, name: 'CA Sales Tax', inclusive: true, applyToShipping: false, applyToDigital: true },
			NY: { rate: 0.08, name: 'NY Sales Tax', inclusive: true, applyToShipping: false, applyToDigital: true },
			TX: { rate: 0.0625, name: 'TX Sales Tax', inclusive: true, applyToShipping: false, applyToDigital: true },
			FL: { rate: 0.06, name: 'FL Sales Tax', inclusive: true, applyToShipping: false, applyToDigital: true },
			WA: { rate: 0.065, name: 'WA Sales Tax', inclusive: true, applyToShipping: false, applyToDigital: true },
			// States without sales tax
			DE: { rate: 0.0, name: 'No Sales Tax', inclusive: true, applyToShipping: false, applyToDigital: false },
			MT: { rate: 0.0, name: 'No Sales Tax', inclusive: true, applyToShipping: false, applyToDigital: false },
			NH: { rate: 0.0, name: 'No Sales Tax', inclusive: true, applyToShipping: false, applyToDigital: false },
			OR: { rate: 0.0, name: 'No Sales Tax', inclusive: true, applyToShipping: false, applyToDigital: false }
		}
	},

	// Canada - GST/HST + Provincial tax (included in our prices)
	CA: {
		default: {
			rate: 0.05, // GST
			name: 'GST',
			inclusive: true, // Our prices include tax
			applyToShipping: true,
			applyToDigital: true
		},
		regions: {
			// HST provinces (combined federal + provincial, included in prices)
			ON: { rate: 0.13, name: 'HST', inclusive: true, applyToShipping: true, applyToDigital: true },
			NS: { rate: 0.15, name: 'HST', inclusive: true, applyToShipping: true, applyToDigital: true },
			NB: { rate: 0.15, name: 'HST', inclusive: true, applyToShipping: true, applyToDigital: true },
			NL: { rate: 0.15, name: 'HST', inclusive: true, applyToShipping: true, applyToDigital: true },
			PE: { rate: 0.15, name: 'HST', inclusive: true, applyToShipping: true, applyToDigital: true },
			// GST + PST provinces (included in prices)
			BC: { rate: 0.12, name: 'GST + PST', inclusive: true, applyToShipping: true, applyToDigital: true },
			SK: { rate: 0.11, name: 'GST + PST', inclusive: true, applyToShipping: true, applyToDigital: true },
			MB: { rate: 0.12, name: 'GST + PST', inclusive: true, applyToShipping: true, applyToDigital: true },
			QC: { rate: 0.14975, name: 'GST + QST', inclusive: true, applyToShipping: true, applyToDigital: true }
		}
	},

	// European Union - VAT
	DE: {
		default: {
			rate: 0.19, // Standard VAT rate
			name: 'VAT',
			inclusive: true, // EU prices typically include VAT
			applyToShipping: true,
			applyToDigital: true
		},
		business: {
			rate: 0.0, // B2B reverse charge
			name: 'VAT (Reverse Charge)',
			inclusive: false,
			applyToShipping: false,
			applyToDigital: false
		}
	},

	FR: {
		default: {
			rate: 0.20,
			name: 'TVA',
			inclusive: true,
			applyToShipping: true,
			applyToDigital: true
		},
		business: {
			rate: 0.0,
			name: 'TVA (Reverse Charge)',
			inclusive: false,
			applyToShipping: false,
			applyToDigital: false
		}
	},

	IT: {
		default: {
			rate: 0.22,
			name: 'IVA',
			inclusive: true,
			applyToShipping: true,
			applyToDigital: true
		},
		business: {
			rate: 0.0,
			name: 'IVA (Reverse Charge)',
			inclusive: false,
			applyToShipping: false,
			applyToDigital: false
		}
	},

	ES: {
		default: {
			rate: 0.21,
			name: 'IVA',
			inclusive: true,
			applyToShipping: true,
			applyToDigital: true
		},
		business: {
			rate: 0.0,
			name: 'IVA (Reverse Charge)',
			inclusive: false,
			applyToShipping: false,
			applyToDigital: false
		}
	},

	NL: {
		default: {
			rate: 0.21,
			name: 'BTW',
			inclusive: true,
			applyToShipping: true,
			applyToDigital: true
		},
		business: {
			rate: 0.0,
			name: 'BTW (Reverse Charge)',
			inclusive: false,
			applyToShipping: false,
			applyToDigital: false
		}
	},

	// United Kingdom - VAT
	GB: {
		default: {
			rate: 0.20,
			name: 'VAT',
			inclusive: true,
			applyToShipping: true,
			applyToDigital: true
		},
		business: {
			rate: 0.0,
			name: 'VAT (Reverse Charge)',
			inclusive: false,
			applyToShipping: false,
			applyToDigital: false
		}
	},

	// Australia - GST
	AU: {
		default: {
			rate: 0.10,
			name: 'GST',
			inclusive: true,
			threshold: 10000, // $100 AUD threshold
			applyToShipping: true,
			applyToDigital: true
		}
	},

	// Japan - Consumption Tax
	JP: {
		default: {
			rate: 0.10,
			name: 'Consumption Tax',
			inclusive: true,
			applyToShipping: true,
			applyToDigital: true
		}
	},

	// China - VAT
	CN: {
		default: {
			rate: 0.13, // Standard rate for goods
			name: 'VAT',
			inclusive: true,
			applyToShipping: true,
			applyToDigital: true
		}
	},

	// New Zealand - GST
	NZ: {
		default: {
			rate: 0.15,
			name: 'GST',
			inclusive: true,
			threshold: 100000, // $1000 NZD threshold
			applyToShipping: true,
			applyToDigital: true
		}
	}
};

/**
 * Gets the tax rule for a specific country and region
 */
export function getTaxRule(
	country: string,
	region?: string,
	isBusiness: boolean = false
): TaxRule {
	const countryConfig = TAX_CONFIG[country];

	if (!countryConfig) {
		// Default to no tax for unknown countries
		return {
			rate: 0.0,
			name: 'No Tax',
			inclusive: false,
			applyToShipping: false,
			applyToDigital: false
		};
	}

	// Business customers in EU get reverse charge
	if (isBusiness && countryConfig.business) {
		return countryConfig.business;
	}

	// Check for region-specific rules
	if (region && countryConfig.regions?.[region]) {
		return countryConfig.regions[region];
	}

	return countryConfig.default;
}

/**
 * Calculates tax amount based on subtotal and tax rule
 */
export function calculateTaxAmount(
	subtotal: number,
	shippingCost: number,
	taxRule: TaxRule
): number {
	if (taxRule.rate === 0) {
		return 0;
	}

	// Check threshold
	if (taxRule.threshold && subtotal < taxRule.threshold) {
		return 0;
	}

	let taxableAmount = subtotal;

	// Add shipping to taxable amount if applicable
	if (taxRule.applyToShipping) {
		taxableAmount += shippingCost;
	}

	// Calculate tax
	if (taxRule.inclusive) {
		// Tax is already included in prices, calculate the tax portion
		return Math.round(taxableAmount * (taxRule.rate / (1 + taxRule.rate)));
	} else {
		// Tax is added on top of prices
		return Math.round(taxableAmount * taxRule.rate);
	}
}

/**
 * Gets display information for tax
 */
export function getTaxDisplayInfo(taxRule: TaxRule): {
	rate: string;
	name: string;
	description: string;
} {
	const percentage = (taxRule.rate * 100).toFixed(1);

	return {
		rate: `${percentage}%`,
		name: taxRule.name,
		description: `${taxRule.name} (${percentage}% included in prices)`
	};
}
