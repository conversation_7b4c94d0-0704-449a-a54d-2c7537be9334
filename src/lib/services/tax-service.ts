/**
 * Tax calculation service for e-commerce
 * Handles international tax rules and calculations
 */

import { getTaxRule, getTaxDisplayInfo, type TaxRule } from '$lib/config/tax-config';

export interface TaxCalculationInput {
	/** Subtotal in cents */
	subtotal: number;
	/** Shipping cost in cents */
	shippingCost: number;
	/** Ship-to country code */
	country: string;
	/** Ship-to state/province/region */
	region?: string;
	/** Whether customer is a business */
	isBusiness?: boolean;
	/** Product types in cart (for future digital goods handling) */
	productTypes?: string[];
}

export interface TaxCalculationResult {
	/** Tax amount included in the prices (in cents) - for display only */
	taxAmountIncluded: number;
	/** Tax rate as decimal */
	taxRate: number;
	/** Tax name (e.g., "VAT", "Sales Tax") */
	taxName: string;
	/** Display rate (e.g., "20%") */
	displayRate: string;
	/** Tax description for UI */
	description: string;
	/** Whether tax is included in prices (always true for our system) */
	inclusive: boolean;
	/** Applied tax rule */
	rule: TaxRule;
}

/**
 * Tax calculation service
 */
export class TaxService {
	/**
	 * Calculates tax included in prices for display purposes
	 * Since all our prices are tax-inclusive, this shows how much tax is included
	 */
	static calculateTax(input: TaxCalculationInput): TaxCalculationResult {
		// Get the applicable tax rule
		const rule = getTaxRule(input.country, input.region, input.isBusiness);

		// Calculate tax amount included in the prices (for display only)
		// Since prices are tax-inclusive, we calculate the tax portion
		const taxAmountIncluded = this.calculateIncludedTax(input.subtotal, input.shippingCost, rule);

		// Get display information
		const displayInfo = getTaxDisplayInfo(rule);

		return {
			taxAmountIncluded,
			taxRate: rule.rate,
			taxName: rule.name,
			displayRate: displayInfo.rate,
			description: displayInfo.description,
			inclusive: true, // Always true for our system
			rule
		};
	}

	/**
	 * Calculates the tax amount that is included in tax-inclusive prices
	 */
	private static calculateIncludedTax(
		subtotal: number,
		shippingCost: number,
		taxRule: TaxRule
	): number {
		if (taxRule.rate === 0) {
			return 0;
		}

		// Check threshold
		if (taxRule.threshold && subtotal < taxRule.threshold) {
			return 0;
		}

		let taxableAmount = subtotal;

		// Add shipping to taxable amount if applicable
		if (taxRule.applyToShipping) {
			taxableAmount += shippingCost;
		}

		// Calculate the tax portion that's included in the price
		// Formula: tax_included = price * (tax_rate / (1 + tax_rate))
		return Math.round(taxableAmount * (taxRule.rate / (1 + taxRule.rate)));
	}

	/**
	 * Determines if tax should be calculated for a given location
	 */
	static shouldCalculateTax(country: string, region?: string): boolean {
		const rule = getTaxRule(country, region);
		return rule.rate > 0;
	}

	/**
	 * Gets tax information without calculating amounts (for display purposes)
	 */
	static getTaxInfo(country: string, region?: string, isBusiness?: boolean): {
		taxName: string;
		displayRate: string;
		description: string;
		inclusive: boolean;
	} {
		const rule = getTaxRule(country, region, isBusiness);
		const displayInfo = getTaxDisplayInfo(rule);

		return {
			taxName: rule.name,
			displayRate: displayInfo.rate,
			description: displayInfo.description,
			inclusive: rule.inclusive
		};
	}

	/**
	 * Validates if we can calculate tax for a given address
	 */
	static canCalculateTax(country: string): boolean {
		// We can calculate tax if we have a country
		// Region is optional for most countries
		return !!country && country.length === 2;
	}

	/**
	 * Gets all supported tax jurisdictions
	 */
	static getSupportedJurisdictions(): Array<{
		country: string;
		regions?: string[];
		hasRegionalTax: boolean;
	}> {
		// This would typically come from the tax config
		// For now, return the major ones
		return [
			{ country: 'US', regions: ['CA', 'NY', 'TX', 'FL', 'WA'], hasRegionalTax: true },
			{ country: 'CA', regions: ['ON', 'BC', 'QC', 'AB'], hasRegionalTax: true },
			{ country: 'DE', hasRegionalTax: false },
			{ country: 'FR', hasRegionalTax: false },
			{ country: 'GB', hasRegionalTax: false },
			{ country: 'AU', hasRegionalTax: false },
			{ country: 'JP', hasRegionalTax: false }
		];
	}
}

/**
 * Helper function for checkout forms to calculate tax dynamically
 */
export function calculateCheckoutTax(
	subtotal: number,
	shippingCost: number,
	country: string,
	region?: string,
	isBusiness?: boolean
): TaxCalculationResult {
	return TaxService.calculateTax({
		subtotal,
		shippingCost,
		country,
		region,
		isBusiness
	});
}

/**
 * Helper function to get tax display text for UI
 */
export function getTaxDisplayText(
	country: string,
	region?: string,
	isBusiness?: boolean
): string {
	const info = TaxService.getTaxInfo(country, region, isBusiness);
	return info.description;
}

/**
 * Helper function to check if tax calculation is needed
 */
export function needsTaxCalculation(country: string, region?: string): boolean {
	return TaxService.shouldCalculateTax(country, region);
}
